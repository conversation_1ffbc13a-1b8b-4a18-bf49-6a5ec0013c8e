{"total": {"lines": {"total": 15012, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15012, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\index.ts": {"lines": {"total": 59, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 59, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\__mocks__\\fs\\promises.ts": {"lines": {"total": 48, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 48, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\analytics\\analytics-service.ts": {"lines": {"total": 261, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 261, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\analytics\\index.ts": {"lines": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\analytics\\supabase-client.ts": {"lines": {"total": 129, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 129, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\analytics\\user-info.ts": {"lines": {"total": 89, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 89, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\codeAssist.ts": {"lines": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\converter.ts": {"lines": {"total": 234, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 234, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\oauth2.ts": {"lines": {"total": 202, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 202, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\server.ts": {"lines": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\setup.ts": {"lines": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\code_assist\\types.ts": {"lines": {"total": 175, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 175, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\config\\config.ts": {"lines": {"total": 502, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 502, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\config\\models.ts": {"lines": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\client.ts": {"lines": {"total": 536, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 536, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\contentGenerator.ts": {"lines": {"total": 132, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 132, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\coreToolScheduler.ts": {"lines": {"total": 671, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 671, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\geminiChat.ts": {"lines": {"total": 622, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 622, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\geminiRequest.ts": {"lines": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\logger.ts": {"lines": {"total": 301, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 301, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\modelCheck.ts": {"lines": {"total": 68, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 68, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\nonInteractiveToolExecutor.ts": {"lines": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\prompts.ts": {"lines": {"total": 273, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 273, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\tokenLimits.ts": {"lines": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\core\\turn.ts": {"lines": {"total": 290, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 290, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\services\\fileDiscoveryService.ts": {"lines": {"total": 93, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 93, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\services\\gitService.ts": {"lines": {"total": 122, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 122, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\constants.ts": {"lines": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\index.ts": {"lines": {"total": 40, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 40, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\loggers.ts": {"lines": {"total": 304, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 304, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\metrics.ts": {"lines": {"total": 202, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 202, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\sdk.ts": {"lines": {"total": 137, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 137, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\types.ts": {"lines": {"total": 217, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 217, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\clearcut-logger\\clearcut-logger.ts": {"lines": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 397, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\telemetry\\clearcut-logger\\event-metadata-key.ts": {"lines": {"total": 153, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 153, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\diffOptions.ts": {"lines": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\edit.ts": {"lines": {"total": 480, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 480, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\glob.ts": {"lines": {"total": 307, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 307, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\grep.ts": {"lines": {"total": 544, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 544, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\ls.ts": {"lines": {"total": 313, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 313, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\mcp-client.ts": {"lines": {"total": 384, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 384, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\mcp-tool.ts": {"lines": {"total": 148, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 148, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\memoryTool.ts": {"lines": {"total": 222, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 222, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\modifiable-tool.ts": {"lines": {"total": 165, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 165, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\read-file.ts": {"lines": {"total": 171, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 171, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\read-many-files.ts": {"lines": {"total": 494, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 494, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\shell.ts": {"lines": {"total": 365, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 365, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\tool-registry.ts": {"lines": {"total": 234, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 234, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\tools.ts": {"lines": {"total": 246, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 246, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\web-fetch.ts": {"lines": {"total": 360, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 360, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\web-search.ts": {"lines": {"total": 195, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 195, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\tools\\write-file.ts": {"lines": {"total": 401, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 401, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\LruCache.ts": {"lines": {"total": 41, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 41, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\bfsFileSearch.ts": {"lines": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\editCorrector.ts": {"lines": {"total": 627, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 627, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\editor.ts": {"lines": {"total": 183, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 183, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\errorReporting.ts": {"lines": {"total": 117, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 117, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\errors.ts": {"lines": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 62, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\fetch.ts": {"lines": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 57, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\fileUtils.ts": {"lines": {"total": 290, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 290, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\generateContentResponseUtilities.ts": {"lines": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 119, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\getFolderStructure.ts": {"lines": {"total": 347, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 347, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\gitIgnoreParser.ts": {"lines": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 79, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\gitUtils.ts": {"lines": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 73, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\memoryDiscovery.ts": {"lines": {"total": 310, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 310, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\messageInspectors.ts": {"lines": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\nextSpeakerChecker.ts": {"lines": {"total": 151, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 151, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\paths.ts": {"lines": {"total": 159, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 159, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\retry.ts": {"lines": {"total": 272, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 272, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\schemaValidator.ts": {"lines": {"total": 58, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 58, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\session.ts": {"lines": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\testUtils.ts": {"lines": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 87, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "C:\\Users\\<USER>\\Downloads\\codecraft cli\\gemini-cli\\packages\\core\\src\\utils\\user_id.ts": {"lines": {"total": 58, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 58, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}}