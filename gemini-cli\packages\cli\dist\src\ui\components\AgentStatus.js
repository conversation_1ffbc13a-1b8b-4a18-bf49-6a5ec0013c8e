import { jsxs as _jsxs, jsx as _jsx, Fragment as _Fragment } from "react/jsx-runtime";
import { Box, Text } from 'ink';
/**
 * Component to display the current agent status
 */
export const AgentStatus = ({ activeAgent, show = true, compact = false, }) => {
    if (!show || !activeAgent) {
        return null;
    }
    if (compact) {
        return (_jsx(Box, { children: _jsxs(Text, { color: "cyan", children: ["\uD83E\uDD16 ", activeAgent.name] }) }));
    }
    return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "cyan", padding: 1, children: [_jsx(Box, { children: _jsxs(Text, { color: "cyan", bold: true, children: ["\uD83E\uDD16 Active Agent: ", activeAgent.name] }) }), _jsx(Box, { children: _jsxs(Text, { color: "gray", children: ["Expertise: ", activeAgent.expertiseArea] }) }), _jsx(Box, { children: _jsx(Text, { color: "gray", children: activeAgent.description }) })] }));
};
export const AgentSuggestions = ({ suggestions, onSelectAgent, show = true, }) => {
    if (!show || suggestions.length === 0) {
        return null;
    }
    return (_jsxs(Box, { flexDirection: "column", borderStyle: "round", borderColor: "yellow", padding: 1, children: [_jsx(Box, { children: _jsx(Text, { color: "yellow", bold: true, children: "\uD83D\uDCA1 Suggested Agents:" }) }), suggestions.slice(0, 3).map((suggestion, index) => (_jsxs(Box, { flexDirection: "column", marginTop: index > 0 ? 1 : 0, children: [_jsx(Box, { children: _jsxs(Text, { color: "white", children: [index + 1, ". ", suggestion.agent.name, " (", Math.round(suggestion.confidence * 100), "% match)"] }) }), _jsx(Box, { children: _jsx(Text, { color: "gray", children: suggestion.agent.description }) }), _jsx(Box, { children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Reasons: ", suggestion.reasons.join(', ')] }) })] }, suggestion.agent.id))), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "Use `/agent use <name>` to activate an agent" }) })] }));
};
export const AgentList = ({ agents, activeAgent, show = true, compact = false, }) => {
    if (!show) {
        return null;
    }
    if (agents.length === 0) {
        return (_jsx(Box, { children: _jsx(Text, { color: "gray", children: "No agents available. Use `/agent create` to create your first agent." }) }));
    }
    return (_jsxs(Box, { flexDirection: "column", children: [_jsx(Box, { children: _jsxs(Text, { color: "cyan", bold: true, children: ["Available Agents (", agents.length, "):"] }) }), agents.map((agent, index) => {
                const isActive = activeAgent?.id === agent.id;
                return (_jsxs(Box, { flexDirection: "column", marginTop: index > 0 ? 1 : 0, children: [_jsx(Box, { children: _jsxs(Text, { color: isActive ? "green" : "white", bold: isActive, children: [isActive ? "▶ " : "  ", agent.name, isActive ? " (ACTIVE)" : ""] }) }), !compact && (_jsxs(_Fragment, { children: [_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: "gray", children: ["Expertise: ", agent.expertiseArea] }) }), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "gray", children: agent.description }) }), _jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["ID: ", agent.id] }) })] }))] }, agent.id));
            })] }));
};
export default AgentStatus;
//# sourceMappingURL=AgentStatus.js.map