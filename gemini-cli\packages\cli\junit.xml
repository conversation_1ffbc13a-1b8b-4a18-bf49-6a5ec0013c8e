<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="485" failures="9" errors="0" time="20.1259114">
    <testsuite name="src/gemini.test.tsx" timestamp="2025-08-05T15:31:47.313Z" hostname="LAPTOP-BGULKM6D" tests="1" failures="0" errors="0" skipped="0" time="0.0262473">
        <testcase classname="src/gemini.test.tsx" name="gemini.tsx main function &gt; should call process.exit(1) if settings have errors" time="0.0227596">
        </testcase>
    </testsuite>
    <testsuite name="src/nonInteractiveCli.test.ts" timestamp="2025-08-05T15:31:47.315Z" hostname="LAPTOP-BGULKM6D" tests="4" failures="0" errors="0" skipped="0" time="0.0446286">
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should process input and write text output" time="0.0233691">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle a single tool call and respond" time="0.0077497">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should handle error during tool execution" time="0.0046139">
        </testcase>
        <testcase classname="src/nonInteractiveCli.test.ts" name="runNonInteractive &gt; should exit with error if sendMessageStream throws initially" time="0.003536">
        </testcase>
    </testsuite>
    <testsuite name="src/config/agentSelector.test.ts" timestamp="2025-08-05T15:31:47.316Z" hostname="LAPTOP-BGULKM6D" tests="22" failures="1" errors="0" skipped="0" time="0.1070018">
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; initialization &gt; should load agents on initialization" time="0.0134762">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; initialization &gt; should have no active agent initially" time="0.0014356">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; getAvailableAgents &gt; should return enabled agents" time="0.0035476">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; getAvailableAgents &gt; should filter out disabled agents" time="0.0013596">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; setActiveAgent &gt; should set active agent by ID" time="0.0011162">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; setActiveAgent &gt; should return false for non-existent agent" time="0.0008059">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; setActiveAgent &gt; should clear active agent when setting to null" time="0.0008196">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; findAgentByName &gt; should find agent by exact name" time="0.0009159">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; findAgentByName &gt; should find agent by ID" time="0.0007745">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; findAgentByName &gt; should be case insensitive" time="0.0006554">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; findAgentByName &gt; should return null for non-existent agent" time="0.0005885">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; suggestAgents &gt; should suggest documentation agent for documentation tasks" time="0.0094102">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; suggestAgents &gt; should suggest testing agent for testing tasks" time="0.0023102">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; suggestAgents &gt; should auto-select high confidence matches" time="0.0017382">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; suggestAgents &gt; should not auto-select low confidence matches" time="0.0515135">
            <failure message="expected &apos;Found 1 potential agent for this task&apos; to contain &apos;No specific agents found&apos;" type="AssertionError">
AssertionError: expected &apos;Found 1 potential agent for this task&apos; to contain &apos;No specific agents found&apos;

Expected: &quot;No specific agents found&quot;
Received: &quot;Found 1 potential agent for this task&quot;

 ❯ src/config/agentSelector.test.ts:198:39
            </failure>
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; suggestAgents &gt; should consider file patterns in suggestions" time="0.0017377">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; applyAgentPrompt &gt; should return original input when no agent is active" time="0.0007906">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; applyAgentPrompt &gt; should apply agent prompt when agent is active" time="0.0010328">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; applyAgentPrompt &gt; should apply specific agent prompt when provided" time="0.0007432">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; getAgentStatus &gt; should return correct status information" time="0.0029463">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; getAgentStatus &gt; should include errors in status" time="0.0016137">
        </testcase>
        <testcase classname="src/config/agentSelector.test.ts" name="AgentSelector &gt; refresh &gt; should reload agents from storage" time="0.0008587">
        </testcase>
    </testsuite>
    <testsuite name="src/config/agentStorage.test.ts" timestamp="2025-08-05T15:31:47.326Z" hostname="LAPTOP-BGULKM6D" tests="27" failures="3" errors="0" skipped="0" time="0.1558732">
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; generateAgentId &gt; should generate a unique ID from name" time="0.0091631">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; generateAgentId &gt; should handle special characters in name" time="0.0401014">
            <failure message="expected &apos;test-agent-------mdyp4xxu&apos; to match /^test-agent----[a-z0-9]+$/" type="AssertionError">
AssertionError: expected &apos;test-agent-------mdyp4xxu&apos; to match /^test-agent----[a-z0-9]+$/

- Expected: 
/^test-agent----[a-z0-9]+$/

+ Received: 
&quot;test-agent-------mdyp4xxu&quot;

 ❯ src/config/agentStorage.test.ts:70:18
            </failure>
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; validateAgent &gt; should return no errors for valid agent" time="0.0050908">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; validateAgent &gt; should return errors for missing required fields" time="0.005014">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; validateAgent &gt; should return errors for invalid field types" time="0.0035451">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; createAgentFromTemplate &gt; should create agent from DOCUMENTATION template" time="0.0042757">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; createAgentFromTemplate &gt; should create agent with custom name" time="0.0019794">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; createAgentFromTemplate &gt; should create agent with custom ID" time="0.0023382">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; getAgentFilePath &gt; should return user agent path" time="0.009476">
            <failure message="expected &apos;C:\Users\<USER>\.codecraft\agents\test…&apos; to be &apos;C:\Users\<USER>\.codecraft\agents/test…&apos; // Object.is equality" type="AssertionError">
AssertionError: expected &apos;C:\Users\<USER>\.codecraft\agents\test…&apos; to be &apos;C:\Users\<USER>\.codecraft\agents/test…&apos; // Object.is equality

Expected: &quot;C:\Users\<USER>\.codecraft\agents/test-agent.json&quot;
Received: &quot;C:\Users\<USER>\.codecraft\agents\test-agent.json&quot;

 ❯ src/config/agentStorage.test.ts:127:20
            </failure>
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; getAgentFilePath &gt; should return workspace agent path" time="0.0039057">
            <failure message="expected &apos;\mock\workspace\.codecraft\agents\tes…&apos; to be &apos;\mock\workspace\.codecraft\agents/tes…&apos; // Object.is equality" type="AssertionError">
AssertionError: expected &apos;\mock\workspace\.codecraft\agents\tes…&apos; to be &apos;\mock\workspace\.codecraft\agents/tes…&apos; // Object.is equality

Expected: &quot;\mock\workspace\.codecraft\agents/test-agent.json&quot;
Received: &quot;\mock\workspace\.codecraft\agents\test-agent.json&quot;

 ❯ src/config/agentStorage.test.ts:132:20
            </failure>
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; saveAgent &gt; should save valid agent" time="0.0071905">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; saveAgent &gt; should create directory if it does not exist" time="0.0060007">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; saveAgent &gt; should throw error for invalid agent" time="0.0072049">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; saveAgent &gt; should update metadata on save" time="0.0025778">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAgentFromFile &gt; should load valid agent file" time="0.0033682">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAgentFromFile &gt; should return error for non-existent file" time="0.0020089">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAgentFromFile &gt; should return error for invalid JSON" time="0.0077378">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAgentFromFile &gt; should return error for invalid agent data" time="0.0023524">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAllAgents &gt; should load agents from both directories" time="0.0048287">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; loadAllAgents &gt; should handle directory read errors" time="0.0029312">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; deleteAgent &gt; should delete existing agent" time="0.0029806">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; deleteAgent &gt; should return false for non-existent agent" time="0.0026671">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; deleteAgent &gt; should throw error on delete failure" time="0.002812">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; agentExists &gt; should return true for existing agent" time="0.0022937">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; agentExists &gt; should return false for non-existent agent" time="0.0021216">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; findAgent &gt; should find and return existing agent" time="0.0027178">
        </testcase>
        <testcase classname="src/config/agentStorage.test.ts" name="Agent Storage &gt; findAgent &gt; should return null for non-existent agent" time="0.0017483">
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.integration.test.ts" timestamp="2025-08-05T15:31:47.333Z" hostname="LAPTOP-BGULKM6D" tests="11" failures="0" errors="0" skipped="0" time="0.2154938">
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load default file filtering settings" time="0.1450788">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should load custom file filtering settings from configuration" time="0.0070033">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; File Filtering Configuration &gt; should merge user and workspace file filtering settings" time="0.0056296">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle partial configuration objects gracefully" time="0.0052994">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle empty configuration objects gracefully" time="0.0063749">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Configuration Integration &gt; should handle missing configuration sections gracefully" time="0.0063605">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a security-focused configuration" time="0.0076796">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Real-world Configuration Scenarios &gt; should handle a CI/CD environment configuration" time="0.0060637">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Checkpointing Configuration &gt; should enable checkpointing when the setting is true" time="0.0058848">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should have an empty array for extension context files by default" time="0.0080459">
        </testcase>
        <testcase classname="src/config/config.integration.test.ts" name="Configuration Integration Tests &gt; Extension Context Files &gt; should correctly store and return extension context file paths" time="0.005528">
        </testcase>
    </testsuite>
    <testsuite name="src/config/config.test.ts" timestamp="2025-08-05T15:31:47.338Z" hostname="LAPTOP-BGULKM6D" tests="23" failures="0" errors="0" skipped="0" time="1.1274387">
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to true when --memory flag is present" time="0.5565547">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false when --memory flag is not present" time="0.0168016">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should set showMemoryUsage to false by default from settings if CLI flag is not present" time="0.0589217">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig &gt; should prioritize CLI flag over settings for showMemoryUsage (CLI true, settings false)" time="0.0319228">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false by default when no flag or setting is present" time="0.0764328">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to true when --telemetry flag is present" time="0.1008042">
            <system-out>
OpenTelemetry SDK started successfully.

            </system-out>
            <system-err>
Accessing resource attributes before async attributes settled

            </system-err>
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should set telemetry to false when --no-telemetry flag is present" time="0.014625">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings true)" time="0.0134949">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry value from settings if CLI flag is not present (settings false)" time="0.0138904">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry CLI flag (true) over settings (false)" time="0.013423">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry CLI flag (false) over settings (true)" time="0.026334">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry OTLP endpoint from settings if CLI flag is not present" time="0.0141974">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-otlp-endpoint CLI flag over settings" time="0.0132017">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default endpoint if no OTLP endpoint is provided via CLI or settings" time="0.0152438">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry target from settings if CLI flag is not present" time="0.0132391">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-target CLI flag over settings" time="0.0138092">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default target if no target is provided via CLI or settings" time="0.0121082">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use telemetry log prompts from settings if CLI flag is not present" time="0.018328">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --telemetry-log-prompts CLI flag (true) over settings (false)" time="0.0149172">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should prioritize --no-telemetry-log-prompts CLI flag (false) over settings (true)" time="0.0111218">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="loadCliConfig telemetry &gt; should use default log prompts (true) if no value is provided via CLI or settings" time="0.0130732">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="Hierarchical Memory Loading (config.ts) - Placeholder Suite &gt; should pass extension context file paths to loadServerHierarchicalMemory" time="0.0232154">
        </testcase>
        <testcase classname="src/config/config.test.ts" name="mergeMcpServers &gt; should not modify the original settings object" time="0.015519">
        </testcase>
    </testsuite>
    <testsuite name="src/config/extension.test.ts" timestamp="2025-08-05T15:31:47.342Z" hostname="LAPTOP-BGULKM6D" tests="2" failures="0" errors="0" skipped="0" time="0.0595165">
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path when GEMINI.md is present" time="0.0370079">
            <system-out>
Loading extension: ext1 (version: 1.0.0)
Loading extension: ext2 (version: 2.0.0)

            </system-out>
        </testcase>
        <testcase classname="src/config/extension.test.ts" name="loadExtensions &gt; should load context file path from the extension config" time="0.0176014">
            <system-out>
Loading extension: ext1 (version: 1.0.0)

            </system-out>
        </testcase>
    </testsuite>
    <testsuite name="src/config/settings.test.ts" timestamp="2025-08-05T15:31:47.342Z" hostname="LAPTOP-BGULKM6D" tests="21" failures="0" errors="0" skipped="0" time="0.0871047">
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load empty settings if no files exist" time="0.0136961">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load user settings if only user file exists" time="0.0073992">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load workspace settings if only workspace file exists" time="0.0032357">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should merge user and workspace settings, with workspace taking precedence" time="0.0030913">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in user settings" time="0.0025961">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle contextFileName correctly when only in workspace settings" time="0.0024257">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should default contextFileName to undefined if not in any settings file" time="0.0022196">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from user settings" time="0.0023263">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should load telemetry setting from workspace settings" time="0.0031518">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace telemetry setting over user setting" time="0.0022972">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should have telemetry as undefined if not in any settings file" time="0.0024028">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should handle JSON parsing errors gracefully" time="0.0051857">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in user settings" time="0.0024022">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in workspace settings" time="0.0023946">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should prioritize workspace env variables over user env variables if keys clash after resolution" time="0.0025653">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should leave unresolved environment variables as is" time="0.0022275">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple environment variables in a single string" time="0.0024358">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve environment variables in arrays" time="0.0030467">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should correctly pass through null, boolean, and number types, and handle undefined properties" time="0.0045649">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; loadSettings &gt; should resolve multiple concatenated environment variables in a single string value" time="0.0027908">
        </testcase>
        <testcase classname="src/config/settings.test.ts" name="Settings Loading and Merging &gt; LoadedSettings class &gt; setValue should update the correct scope and recompute merged settings" time="0.0051601">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/App.test.tsx" timestamp="2025-08-05T15:31:47.345Z" hostname="LAPTOP-BGULKM6D" tests="10" failures="0" errors="0" skipped="0" time="2.7740298">
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;GEMINI.md&quot; in footer when contextFileName is not set and count is 1" time="0.4385528">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display default &quot;GEMINI.md&quot; with plural when contextFileName is not set and count is &gt; 1" time="0.2318499">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName in footer when set and count is 1" time="0.4151375">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display a generic message when multiple context files with different names are provided" time="0.2493292">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display custom contextFileName with plural when set and count is &gt; 1" time="0.228831">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should not display context file message if count is 0, even if contextFileName is set" time="0.0936788">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display GEMINI.md and MCP server count when both are present" time="0.2448283">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; should display only MCP server count when GEMINI.md count is 0" time="0.1050749">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display theme dialog if NO_COLOR is not set" time="0.5209608">
        </testcase>
        <testcase classname="src/ui/App.test.tsx" name="App UI &gt; when no theme is set &gt; should display a message if NO_COLOR is set" time="0.2392572">
        </testcase>
    </testsuite>
    <testsuite name="src/utils/startupWarnings.test.ts" timestamp="2025-08-05T15:31:47.347Z" hostname="LAPTOP-BGULKM6D" tests="4" failures="0" errors="0" skipped="4" time="0">
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return warnings from the file and delete it" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an empty array if the file does not exist" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return an error message if reading the file fails" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/utils/startupWarnings.test.ts" name="startupWarnings &gt; should return a warning if deleting the file fails" time="0">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/AuthDialog.test.tsx" timestamp="2025-08-05T15:31:47.348Z" hostname="LAPTOP-BGULKM6D" tests="3" failures="0" errors="0" skipped="0" time="0.382279">
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should show an error if the initial auth type is invalid" time="0.0646979">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should prevent exiting when no auth method is selected and show error message" time="0.1747033">
        </testcase>
        <testcase classname="src/ui/components/AuthDialog.test.tsx" name="AuthDialog &gt; should allow exiting when auth method is already selected" time="0.1382345">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/HistoryItemDisplay.test.tsx" timestamp="2025-08-05T15:31:47.349Z" hostname="LAPTOP-BGULKM6D" tests="4" failures="1" errors="0" skipped="0" time="0.7110755">
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders UserMessage for &quot;user&quot; type" time="0.1284247">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders StatsDisplay for &quot;stats&quot; type" time="0.2163054">
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders AboutBox for &quot;about&quot; type" time="0.2380919">
            <failure message="expected &apos;\n╭──────────────────────────────────…&apos; to contain &apos;About Gemini CLI&apos;" type="AssertionError">
AssertionError: expected &apos;\n╭──────────────────────────────────…&apos; to contain &apos;About Gemini CLI&apos;

- Expected
+ Received

- About Gemini CLI
+
+ ╭──────────────────────────────────────────────────────────────────────────────────────────────────╮
+ │                                                                                                  │
+ │ About CodeCraft CLI                                                                              │
+ │                                                                                                  │
+ │ CLI Version                       1.0.0                                                          │
+ │ Git Commit                        f7409723 (local modifications)                                 │
+ │ Model                             test-model                                                     │
+ │ Sandbox                           test-env                                                       │
+ │ OS                                test-os                                                        │
+ │                                                                                                  │
+ ╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
+

 ❯ src/ui/components/HistoryItemDisplay.test.tsx:74:25
            </failure>
        </testcase>
        <testcase classname="src/ui/components/HistoryItemDisplay.test.tsx" name="&lt;HistoryItemDisplay /&gt; &gt; renders SessionSummaryDisplay for &quot;quit&quot; type" time="0.1207006">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/InputPrompt.test.tsx" timestamp="2025-08-05T15:31:47.350Z" hostname="LAPTOP-BGULKM6D" tests="5" failures="0" errors="0" skipped="0" time="1.3714999">
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getPreviousCommand on up arrow in shell mode" time="0.2822461">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.getNextCommand on down arrow in shell mode" time="0.1395319">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should set the buffer text when a shell history command is retrieved" time="0.2483076">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should call shellHistory.addCommandToHistory on submit in shell mode" time="0.2008578">
        </testcase>
        <testcase classname="src/ui/components/InputPrompt.test.tsx" name="InputPrompt &gt; should NOT call shell history methods when not in shell mode" time="0.4912109">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/LoadingIndicator.test.tsx" timestamp="2025-08-05T15:31:47.352Z" hostname="LAPTOP-BGULKM6D" tests="10" failures="0" errors="0" skipped="0" time="0.1796686">
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should not render when streamingState is Idle" time="0.0650282">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner, phrase, and time when streamingState is Responding" time="0.0356961">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render spinner (static), phrase but no time/cancel when streamingState is WaitingForConfirmation" time="0.0092728">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the currentLoadingPhrase correctly" time="0.0062934">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the elapsedTime correctly when Responding" time="0.0061157">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should render rightContent when provided" time="0.0058072">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should transition correctly between states using rerender" time="0.0256832">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display fallback phrase if thought is empty" time="0.0057143">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should display the subject of a thought" time="0.0074023">
        </testcase>
        <testcase classname="src/ui/components/LoadingIndicator.test.tsx" name="&lt;LoadingIndicator /&gt; &gt; should prioritize thought.subject over currentLoadingPhrase" time="0.0055775">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/SessionSummaryDisplay.test.tsx" timestamp="2025-08-05T15:31:47.355Z" hostname="LAPTOP-BGULKM6D" tests="2" failures="0" errors="0" skipped="0" time="0.2042122">
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.1765316">
        </testcase>
        <testcase classname="src/ui/components/SessionSummaryDisplay.test.tsx" name="&lt;SessionSummaryDisplay /&gt; &gt; renders zero state correctly" time="0.0224102">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/Stats.test.tsx" timestamp="2025-08-05T15:31:47.356Z" hostname="LAPTOP-BGULKM6D" tests="7" failures="0" errors="0" skipped="0" time="0.3130498">
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders a label and value" time="0.2253412">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatRow /&gt; &gt; renders with a specific value color" time="0.0057421">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with children" time="0.0329231">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a stats column with a specific width" time="0.0123004">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; renders a cumulative stats column with percentages" time="0.0131443">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;StatsColumn /&gt; &gt; hides the tool use row when there are no tool use tokens" time="0.0105297">
        </testcase>
        <testcase classname="src/ui/components/Stats.test.tsx" name="&lt;DurationColumn /&gt; &gt; renders a duration column" time="0.0061692">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/StatsDisplay.test.tsx" timestamp="2025-08-05T15:31:47.357Z" hostname="LAPTOP-BGULKM6D" tests="2" failures="0" errors="0" skipped="0" time="0.3517416">
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders correctly with given stats and duration" time="0.3201191">
        </testcase>
        <testcase classname="src/ui/components/StatsDisplay.test.tsx" name="&lt;StatsDisplay /&gt; &gt; renders zero state correctly" time="0.0243335">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/contexts/SessionContext.test.tsx" timestamp="2025-08-05T15:31:47.358Z" hostname="LAPTOP-BGULKM6D" tests="6" failures="0" errors="0" skipped="0" time="0.9597429">
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should provide the correct initial state" time="0.0648471">
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should increment turnCount when startNewTurn is called" time="0.18694">
            <system-err>
Warning: `ReactDOMTestUtils.act` is deprecated in favor of `React.act`. Import `act` from `react` instead of `react-dom/test-utils`. See https://react.dev/warnings/react-dom-test-utils for more info.

            </system-err>
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should aggregate token usage correctly when addUsage is called" time="0.1193112">
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should correctly track a full logical turn with multiple API calls" time="0.0618342">
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should overwrite currentResponse with each API call" time="0.0044484">
        </testcase>
        <testcase classname="src/ui/contexts/SessionContext.test.tsx" name="SessionStatsContext &gt; should throw an error when useSessionStats is used outside of a provider" time="0.510118">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/atCommandProcessor.test.ts" timestamp="2025-08-05T15:31:47.359Z" hostname="LAPTOP-BGULKM6D" tests="17" failures="1" errors="0" skipped="0" time="0.1750589">
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through query if no @ command is present" time="0.0251414">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should pass through original query if only a lone @ symbol is present" time="0.0041281">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid text file path" time="0.0153915">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid directory path and convert to glob" time="0.0055326">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a valid image file path (as text content for now)" time="0.0025595">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle query with text before and after @command" time="0.0042758">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should correctly unescape paths with escaped spaces" time="0.0039289">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references" time="0.0045286">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle multiple @file references with interleaved text" time="0.0049884">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should handle a mix of valid, invalid, and lone @ references" time="0.045748">
            <failure message="expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ][90m

Received: 

[1m  1st spy call:

[22m[2m  [[22m
[2m    {[22m
[2m      &quot;paths&quot;: [[22m
[2m        &quot;valid1.txt&quot;,[22m
[32m-       &quot;resolved/valid2.actual&quot;,[90m
[31m+       &quot;resolved\\valid2.actual&quot;,[90m
[2m      ],[22m
[2m      &quot;respectGitIgnore&quot;: true,[22m
[2m    },[22m
[2m    AbortSignal {},[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;spy&quot; to be called with arguments: [ { paths: [ …(2) ], …(1) }, …(1) ]

Received: 

  1st spy call:

  [
    {
      &quot;paths&quot;: [
        &quot;valid1.txt&quot;,
-       &quot;resolved/valid2.actual&quot;,
+       &quot;resolved\\valid2.actual&quot;,
      ],
      &quot;respectGitIgnore&quot;: true,
    },
    AbortSignal {},
  ]


Number of calls: 1

 ❯ src/ui/hooks/atCommandProcessor.test.ts:456:38
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should return original query if all @paths are invalid or lone @" time="0.0264246">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; should process a file path case-insensitively" time="0.0058608">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should skip git-ignored files in @ commands" time="0.004473">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should process non-git-ignored files normally" time="0.004511">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should handle mixed git-ignored and valid files" time="0.0050851">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; git-aware filtering &gt; should always ignore .git directory files" time="0.0025878">
        </testcase>
        <testcase classname="src/ui/hooks/atCommandProcessor.test.ts" name="handleAtCommand &gt; when recursive file search is disabled &gt; should not use glob search for a nonexistent file" time="0.0033579">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/shellCommandProcessor.test.ts" timestamp="2025-08-05T15:31:47.363Z" hostname="LAPTOP-BGULKM6D" tests="3" failures="0" errors="0" skipped="0" time="0.2767973">
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should execute a command and update history on success" time="0.1535227">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle binary output" time="0.0435183">
        </testcase>
        <testcase classname="src/ui/hooks/shellCommandProcessor.test.ts" name="useShellCommandProcessor &gt; should handle command failure" time="0.0733862">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/slashCommandProcessor.test.ts" timestamp="2025-08-05T15:31:47.363Z" hostname="LAPTOP-BGULKM6D" tests="30" failures="0" errors="0" skipped="0" time="0.4149616">
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should return tool scheduling info on valid input" time="0.065332">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory add &gt; should show usage error and return true if no text is provided" time="0.0112402">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory show &gt; should call the showMemoryAction and return true" time="0.0165821">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /memory refresh &gt; should call performMemoryRefresh and return true" time="0.0108519">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown /memory subcommand &gt; should show an error for unknown /memory subcommand and return true" time="0.0112418">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /stats command &gt; should show detailed session statistics" time="0.01227">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /help should open help and return true" time="0.0092328">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /clear should clear items, reset chat, and refresh static" time="0.0098686">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Other commands &gt; /editor should open editor dialog and return true" time="0.0096677">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should call open with the correct GitHub issue URL and return true" time="0.015334">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /bug command &gt; should use the custom bug command URL from config if available" time="0.0136579">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /quit, set quitting messages, and exit the process" time="0.0215824">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /quit and /exit commands &gt; should handle /exit, set quitting messages, and exit the process" time="0.0109426">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; Unknown command &gt; should show an error and return true for a general unknown command" time="0.0090111">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if tool registry is not available" time="0.018807">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should show an error if getAllTools returns undefined" time="0.0099361">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display only Gemini CLI tools (filtering out MCP tools)" time="0.0097098">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display a message when no Gemini CLI tools are available" time="0.0083879">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /tools command &gt; should display tool descriptions when /tools desc is used" time="0.0103928">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show an error if tool registry is not available" time="0.0099279">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message with a URL when no MCP servers are configured in a sandbox" time="0.0108945">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display a message and open a URL when no MCP servers are configured outside a sandbox" time="0.0106462">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display configured MCP servers with status indicators and their tools" time="0.0109065">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should display tool descriptions when showToolDescriptions is true" time="0.012492">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should indicate when a server has no tools" time="0.0100089">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp command &gt; should show startup indicator when servers are connecting" time="0.0097338">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /mcp schema &gt; should display tool schemas and descriptions" time="0.0106453">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /compress command &gt; should call tryCompressChat(true)" time="0.0198994">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /agent command &gt; should show error when no project root is available" time="0.0072147">
        </testcase>
        <testcase classname="src/ui/hooks/slashCommandProcessor.test.ts" name="useSlashCommandProcessor &gt; /agent command &gt; should show error for unknown agent subcommand" time="0.009743">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useAutoAcceptIndicator.test.ts" timestamp="2025-08-05T15:31:47.367Z" hostname="LAPTOP-BGULKM6D" tests="6" failures="0" errors="0" skipped="0" time="0.1867566">
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.AUTO_EDIT if config.getApprovalMode returns ApprovalMode.AUTO_EDIT" time="0.0589933">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.DEFAULT if config.getApprovalMode returns ApprovalMode.DEFAULT" time="0.0068065">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should initialize with ApprovalMode.YOLO if config.getApprovalMode returns ApprovalMode.YOLO" time="0.0389412">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should toggle the indicator and update config when Shift+Tab or Ctrl+Y is pressed" time="0.0263683">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should not toggle if only one key or other keys combinations are pressed" time="0.0333788">
        </testcase>
        <testcase classname="src/ui/hooks/useAutoAcceptIndicator.test.ts" name="useAutoAcceptIndicator &gt; should update indicator when config value changes externally (useEffect dependency)" time="0.0152906">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useCompletion.integration.test.ts" timestamp="2025-08-05T15:31:47.369Z" hostname="LAPTOP-BGULKM6D" tests="9" failures="3" errors="0" skipped="0" time="1.6495289">
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored entries from @ completions" time="0.2514823">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should filter git-ignored directories from @ completions" time="0.1683378">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle recursive search with git-aware filtering" time="0.1781739">
            <system-err>
Error fetching completion suggestions for t: Cannot read properties of undefined (reading &apos;map&apos;)

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should not perform recursive search when disabled in config" time="0.1944435">
            <failure message="expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ][90m

Received: 

[1m  1st readdir call:

[22m[2m  [[22m
[32m-   &quot;/test/project&quot;,[90m
[31m+   &quot;C:\\test\\project&quot;,[90m
[2m    {[22m
[2m      &quot;withFileTypes&quot;: true,[22m
[2m    },[22m
[2m  ][22m
[39m[90m

Number of calls: [1m1[22m
[39m" type="AssertionError">
AssertionError: expected &quot;readdir&quot; to be called with arguments: [ &apos;/test/project&apos;, …(1) ]

Received: 

  1st readdir call:

  [
-   &quot;/test/project&quot;,
+   &quot;C:\\test\\project&quot;,
    {
      &quot;withFileTypes&quot;: true,
    },
  ]


Number of calls: 1

 ❯ src/ui/hooks/useCompletion.integration.test.ts:200:24
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should work without config (fallback behavior)" time="0.1638105">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle git discovery service initialization failure gracefully" time="0.1713563">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should handle directory-specific completions with git filtering" time="0.1782255">
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should use glob for top-level @ completions when available" time="0.1712828">
            <failure message="expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]" type="AssertionError">
AssertionError: expected [ { label: &apos;README.md&apos;, …(1) }, …(1) ] to deeply equal [ { label: &apos;README.md&apos;, …(1) }, …(1) ]

- Expected
+ Received

@@ -2,9 +2,9 @@
    {
      &quot;label&quot;: &quot;README.md&quot;,
      &quot;value&quot;: &quot;README.md&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:297:40
            </failure>
        </testcase>
        <testcase classname="src/ui/hooks/useCompletion.integration.test.ts" name="useCompletion git-aware filtering integration &gt; should include dotfiles in glob search when input starts with a dot" time="0.1649722">
            <failure message="expected [ …(3) ] to deeply equal [ …(3) ]" type="AssertionError">
AssertionError: expected [ …(3) ] to deeply equal [ …(3) ]

- Expected
+ Received

@@ -6,9 +6,9 @@
    {
      &quot;label&quot;: &quot;.gitignore&quot;,
      &quot;value&quot;: &quot;.gitignore&quot;,
    },
    {
-     &quot;label&quot;: &quot;src/index.ts&quot;,
-     &quot;value&quot;: &quot;src/index.ts&quot;,
+     &quot;label&quot;: &quot;src\\index.ts&quot;,
+     &quot;value&quot;: &quot;src\\index.ts&quot;,
    },
  ]

 ❯ src/ui/hooks/useCompletion.integration.test.ts:325:40
            </failure>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useConsoleMessages.test.ts" timestamp="2025-08-05T15:31:47.372Z" hostname="LAPTOP-BGULKM6D" tests="9" failures="0" errors="0" skipped="0" time="0.2656227">
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should initialize with an empty array of console messages" time="0.135555">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should add a new message" time="0.0124604">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should consolidate identical consecutive messages" time="0.0055993">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate different messages" time="0.0078691">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should not consolidate messages if type is different" time="0.0087535">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear console messages" time="0.008945">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear pending timeout on clearConsoleMessages" time="0.0080028">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should clear message queue on clearConsoleMessages" time="0.0054577">
        </testcase>
        <testcase classname="src/ui/hooks/useConsoleMessages.test.ts" name="useConsoleMessages &gt; should cleanup timeout on unmount" time="0.0636844">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useEditorSettings.test.ts" timestamp="2025-08-05T15:31:47.374Z" hostname="LAPTOP-BGULKM6D" tests="10" failures="0" errors="0" skipped="0" time="0.183351">
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should initialize with dialog closed" time="0.042696">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should open editor dialog when openEditorDialog is called" time="0.0111112">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should close editor dialog when exitEditorDialog is called" time="0.0057045">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle editor selection successfully" time="0.0599746">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle clearing editor preference (undefined editor)" time="0.0083488">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different editor types" time="0.0207702">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle different setting scopes" time="0.0120134">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for unavailable editors" time="0.0060538">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should not set preference for editors not allowed in sandbox" time="0.0048225">
        </testcase>
        <testcase classname="src/ui/hooks/useEditorSettings.test.ts" name="useEditorSettings &gt; should handle errors during editor selection" time="0.0047555">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useGeminiStream.test.tsx" timestamp="2025-08-05T15:31:47.375Z" hostname="LAPTOP-BGULKM6D" tests="24" failures="0" errors="0" skipped="0" time="1.1716">
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should merge multiple PartListUnion arrays" time="0.0127174">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should handle empty arrays in the input list" time="0.0012183">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should handle a single PartListUnion array" time="0.0008334">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should return an empty array if all input arrays are empty" time="0.0008917">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should handle input list being empty" time="0.0008597">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should correctly merge when PartListUnion items are single Parts not in arrays" time="0.0010221">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should handle a mix of arrays and single parts, including empty arrays and undefined/null parts if they were possible (though PartListUnion typing restricts this)" time="0.0011004">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should preserve the order of parts from the input arrays" time="0.0009911">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="mergePartListUnions &gt; should handle cases where some PartListUnion items are single Parts and others are arrays of Parts" time="0.0013479">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; should not submit tool responses if not all tool calls are completed" time="0.1318595">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; should submit tool responses when all tool calls are completed and ready" time="0.1072194">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; should handle all tool calls being cancelled" time="0.101966">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Session Stats Integration &gt; should call startNewTurn and addUsage for a simple prompt" time="0.324886">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:621:30
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:620:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Session Stats Integration &gt; should only call addUsage for a tool continuation prompt" time="0.0193523">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Session Stats Integration &gt; should not call addUsage if the stream contains no usage metadata" time="0.0191952">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:660:30
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:659:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Session Stats Integration &gt; should not call startNewTurn for a slash command" time="0.050391">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:673:30
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:672:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; should not flicker streaming state to Idle between tool completion and submission" time="0.0695143">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; User Cancellation &gt; should cancel an in-progress stream when escape is pressed" time="0.095171">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:784:24
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:783:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; User Cancellation &gt; should not do anything if escape is pressed when not responding" time="0.0143613">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; User Cancellation &gt; should prevent further processing after cancellation" time="0.0969893">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:843:24
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:842:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

Warning: An update to TestComponent inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() =&gt; {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you&apos;re testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
    at TestComponent (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\@testing-library\react\dist\pure.js:307:5)

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; User Cancellation &gt; should not cancel if a tool call is in progress (not just responding)" time="0.0162265">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Client-Initiated Tool Calls &gt; should execute a client-initiated tool without sending a response to Gemini" time="0.0285441">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:957:30
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:956:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Memory Refresh on save_memory &gt; should call performMemoryRefresh when a save_memory tool call completes successfully" time="0.025745">
        </testcase>
        <testcase classname="src/ui/hooks/useGeminiStream.test.tsx" name="useGeminiStream &gt; Error Handling &gt; should call parseAndFormatApiError with the correct authType on stream initialization failure" time="0.0376116">
            <system-out>
Analytics service initialization failed: TypeError: config.getModel is not a function
    at logUserPrompt (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\core\src\telemetry\loggers.ts:109:30)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:226:9
    at Object.submitQuery [90m(C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.ts:542:52[90m)[39m
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:1080:30
    at C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:48:24
    at act (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4mreact[24m\cjs\react.development.js:2512:16)
    at Proxy.&lt;anonymous&gt; (C:\Users\<USER>\Downloads\codecraft cli\gemini-cli\node_modules\[4m@testing-library[24m\react\dist\act-compat.js:47:25)
    at [90mC:\Users\<USER>\Downloads\codecraft cli\gemini-cli\packages\cli\[39msrc\ui\hooks\useGeminiStream.test.tsx:1079:13
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:154:11
    at file:///C:/Users/<USER>/Downloads/codecraft%20cli/gemini-cli/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:751:26

            </system-out>
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useGitBranchName.test.ts" timestamp="2025-08-05T15:31:47.379Z" hostname="LAPTOP-BGULKM6D" tests="7" failures="0" errors="0" skipped="2" time="0.3788601">
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return branch name" time="0.1463908">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if git command fails" time="0.0100977">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return short commit hash if branch is HEAD (detached state)" time="0.008199">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should return undefined if branch is HEAD and getting commit hash fails" time="0.0081478">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should update branch name when .git/HEAD changes" time="0.0036943">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should handle watcher setup error silently" time="0.1457199">
        </testcase>
        <testcase classname="src/ui/hooks/useGitBranchName.test.ts" name="useGitBranchName &gt; should cleanup watcher on unmount" time="0.0497961">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useHistoryManager.test.ts" timestamp="2025-08-05T15:31:47.380Z" hostname="LAPTOP-BGULKM6D" tests="8" failures="0" errors="0" skipped="0" time="0.2953995">
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should initialize with an empty history" time="0.0911956">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add an item to history with a unique ID" time="0.1334554">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should generate unique IDs for items added with the same base timestamp" time="0.0295737">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should update an existing history item" time="0.0090419">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not change history if updateHistoryItem is called with a non-existent ID" time="0.007705">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should clear the history" time="0.0061433">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should not add consecutive duplicate user messages" time="0.0054996">
        </testcase>
        <testcase classname="src/ui/hooks/useHistoryManager.test.ts" name="useHistoryManager &gt; should add duplicate user messages if they are not consecutive" time="0.0057278">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useInputHistory.test.ts" timestamp="2025-08-05T15:31:47.381Z" hostname="LAPTOP-BGULKM6D" tests="11" failures="0" errors="0" skipped="0" time="0.6265065">
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; should initialize with historyIndex -1 and empty originalQueryBeforeNav" time="0.1381946">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should call onSubmit with trimmed value and reset history" time="0.0935753">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; handleSubmit &gt; should not call onSubmit if value is empty after trimming" time="0.0050899">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if isActive is false" time="0.0068925">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should not navigate if userMessages is empty" time="0.0049576">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should call onChange with the last message when navigating up from initial state" time="0.0082792">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should store currentQuery as originalQueryBeforeNav on first navigateUp" time="0.0906896">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateUp &gt; should navigate through history messages on subsequent navigateUp calls" time="0.1472312">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if isActive is false" time="0.0643071">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should not navigate if historyIndex is -1 (not in history navigation)" time="0.0057805">
        </testcase>
        <testcase classname="src/ui/hooks/useInputHistory.test.ts" name="useInputHistory &gt; navigateDown &gt; should restore originalQueryBeforeNav when navigating down to initial state" time="0.0537866">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useLoadingIndicator.test.ts" timestamp="2025-08-05T15:31:47.384Z" hostname="LAPTOP-BGULKM6D" tests="5" failures="0" errors="0" skipped="0" time="0.1595149">
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should initialize with default values when Idle" time="0.0832613">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reflect values when Responding" time="0.026551">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should show waiting phrase and retain elapsedTime when WaitingForConfirmation" time="0.0152369">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset elapsedTime and use a witty phrase when transitioning from WaitingForConfirmation to Responding" time="0.0167711">
        </testcase>
        <testcase classname="src/ui/hooks/useLoadingIndicator.test.ts" name="useLoadingIndicator &gt; should reset timer and phrase when streamingState changes from Responding to Idle" time="0.0111072">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/usePhraseCycler.test.ts" timestamp="2025-08-05T15:31:47.384Z" hostname="LAPTOP-BGULKM6D" tests="7" failures="0" errors="0" skipped="0" time="0.133024">
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should initialize with the first witty phrase when not active and not waiting" time="0.0647052">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should show &quot;Waiting for user confirmation...&quot; when isWaiting is true" time="0.0114297">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should not cycle phrases if isActive is false and not waiting" time="0.0057072">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should cycle through witty phrases when isActive is true and not waiting" time="0.0122845">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when isActive becomes true after being false (and not waiting)" time="0.0177944">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should clear phrase interval on unmount when active" time="0.0056829">
        </testcase>
        <testcase classname="src/ui/hooks/usePhraseCycler.test.ts" name="usePhraseCycler &gt; should reset to a witty phrase when transitioning from waiting to active" time="0.0083695">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useShellHistory.test.ts" timestamp="2025-08-05T15:31:47.386Z" hostname="LAPTOP-BGULKM6D" tests="7" failures="0" errors="0" skipped="0" time="0.7654401">
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should initialize and read the history file from the correct path" time="0.1058596">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should handle a non-existent history file gracefully" time="0.0724105">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should add a command and write to the history file" time="0.147734">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should navigate history correctly with previous/next commands" time="0.0807324">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should not add empty or whitespace-only commands to history" time="0.0673109">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should truncate history to MAX_HISTORY_LENGTH (100)" time="0.1355706">
        </testcase>
        <testcase classname="src/ui/hooks/useShellHistory.test.ts" name="useShellHistory &gt; should move an existing command to the top when re-added" time="0.1493138">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useTimer.test.ts" timestamp="2025-08-05T15:31:47.387Z" hostname="LAPTOP-BGULKM6D" tests="8" failures="0" errors="0" skipped="0" time="0.2801998">
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should initialize with 0" time="0.0642924">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should not increment time if isActive is false" time="0.0066321">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should increment time every second if isActive is true" time="0.0984618">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 and start incrementing when isActive becomes true from false" time="0.0095023">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should reset to 0 when resetKey changes while active" time="0.0087326">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should be 0 if isActive is false, regardless of resetKey changes" time="0.006323">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should clear timer on unmount" time="0.0670447">
        </testcase>
        <testcase classname="src/ui/hooks/useTimer.test.ts" name="useTimer &gt; should preserve elapsedTime when isActive becomes false, and reset to 0 when it becomes active again" time="0.0115278">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/hooks/useToolScheduler.test.ts" timestamp="2025-08-05T15:31:47.388Z" hostname="LAPTOP-BGULKM6D" tests="21" failures="0" errors="0" skipped="4" time="0.3124009">
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler in YOLO Mode &gt; should skip confirmation and execute tool directly when yoloMode is true" time="0.218414">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; initial state should be empty" time="0.0057553">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute a tool call successfully" time="0.0209114">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool not found" time="0.0120367">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during shouldConfirmExecute" time="0.0111804">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle error during execute" time="0.009862">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - approved" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle tool requiring confirmation - cancelled by user" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should handle live output updates" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should schedule and execute multiple tool calls" time="0.0179402">
            <system-err>
Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

Error flushing log events: Error: getaddrinfo ENOTFOUND play.googleapis.com
[90m    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)[39m {
  errno: [33m-3008[39m,
  code: [32m&apos;ENOTFOUND&apos;[39m,
  syscall: [32m&apos;getaddrinfo&apos;[39m,
  hostname: [32m&apos;play.googleapis.com&apos;[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="useReactToolScheduler &gt; should throw error if scheduling while already running" time="0">
            <skipped/>
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;validating&apos; (validating) correctly" time="0.0024327">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;awaiting_approval&apos; (awaiting_approval) correctly" time="0.0008994">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;scheduled&apos; (scheduled) correctly" time="0.0006561">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing no live output) correctly" time="0.0006398">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;executing&apos; (executing with live output) correctly" time="0.000639">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;success&apos; (success) correctly" time="0.0006804">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool not found) correctly" time="0.000651">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;error&apos; (error tool execution failed) correctly" time="0.0005764">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map ToolCall with status &apos;cancelled&apos; (cancelled) correctly" time="0.0007766">
        </testcase>
        <testcase classname="src/ui/hooks/useToolScheduler.test.ts" name="mapToDisplay &gt; should map an array of ToolCalls correctly" time="0.0010615">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/errorParsing.test.ts" timestamp="2025-08-05T15:31:47.391Z" hostname="LAPTOP-BGULKM6D" tests="11" failures="0" errors="0" skipped="0" time="0.0241663">
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a valid API error JSON" time="0.0062756">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the default message" time="0.0016624">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the personal message" time="0.0009527">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 API error with the vertex message" time="0.0008595">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message if it is not a JSON error" time="0.0003884">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should return the original message for malformed JSON" time="0.0014839">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle JSON that does not match the ApiError structure" time="0.0009091">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a nested API error" time="0.001018">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a StructuredError" time="0.0008691">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should format a 429 StructuredError with the vertex message" time="0.0006221">
        </testcase>
        <testcase classname="src/ui/utils/errorParsing.test.ts" name="parseAndFormatApiError &gt; should handle an unknown error type" time="0.0014764">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/formatters.test.ts" timestamp="2025-08-05T15:31:47.392Z" hostname="LAPTOP-BGULKM6D" tests="14" failures="0" errors="0" skipped="0" time="0.0170801">
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into KB" time="0.0052249">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into MB" time="0.0006441">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatMemoryUsage &gt; should format bytes into GB" time="0.0004634">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format milliseconds less than a second" time="0.0008661">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration of 0" time="0.0004728">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of seconds" time="0.000421">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in seconds with one decimal place" time="0.0003222">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of minutes" time="0.0003451">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in minutes and seconds" time="0.0003635">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format an exact number of hours" time="0.0002833">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours and seconds" time="0.0003167">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should format a duration in hours, minutes, and seconds" time="0.0003315">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle large durations" time="0.000387">
        </testcase>
        <testcase classname="src/ui/utils/formatters.test.ts" name="formatters &gt; formatDuration &gt; should handle negative durations" time="0.0003521">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/markdownUtilities.test.ts" timestamp="2025-08-05T15:31:47.394Z" hostname="LAPTOP-BGULKM6D" tests="7" failures="0" errors="0" skipped="0" time="0.0154064">
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should split at the last double newline if not in a code block" time="0.0061515">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if no safe split point is found" time="0.0009518">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should prioritize splitting at 

 over being at the very end of the string if the end is not in a code block" time="0.0004484">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if the only 

 is inside a code block and the end of content is not" time="0.0004233">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should correctly identify the last 

 even if it is followed by text not in a code block" time="0.0004943">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content is empty" time="0.0004208">
        </testcase>
        <testcase classname="src/ui/utils/markdownUtilities.test.ts" name="markdownUtilities &gt; findLastSafeSplitPoint &gt; should return content.length if content has no newlines and no code blocks" time="0.0004901">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/utils/textUtils.test.ts" timestamp="2025-08-05T15:31:47.394Z" hostname="LAPTOP-BGULKM6D" tests="5" failures="0" errors="0" skipped="0" time="0.0138946">
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return true for a buffer containing a null byte" time="0.0055359">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a buffer containing only text" time="0.0008051">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for an empty buffer" time="0.0005834">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should return false for a null or undefined buffer" time="0.0006146">
        </testcase>
        <testcase classname="src/ui/utils/textUtils.test.ts" name="textUtils &gt; isBinary &gt; should only check the sample size" time="0.0007749">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/DiffRenderer.test.tsx" timestamp="2025-08-05T15:31:47.395Z" hostname="LAPTOP-BGULKM6D" tests="13" failures="0" errors="0" skipped="0" time="2.0222826">
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with correct language for new file with known extension" time="0.5107605">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file with unknown extension" time="0.6056812">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should call colorizeCode with null language for new file if no filename is provided" time="0.1065134">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render diff content for existing file (not calling colorizeCode directly for the whole block)" time="0.0254906">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle diff with only header and no changes" time="0.0111711">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should handle empty diff content" time="0.2091032">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should render a gap indicator for skipped lines" time="0.1763386">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should not render a gap indicator for small gaps (&lt;= MAX_CONTEXT_LINES_WITHOUT_GAP)" time="0.1100293">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height undefined" time="0.0836845">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 80 and height 6" time="0.0480946">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with multiple hunks and a gap indicator &gt; with terminalWidth 30 and height 6" time="0.0680698">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a diff with a SVN diff format" time="0.0151353">
        </testcase>
        <testcase classname="src/ui/components/messages/DiffRenderer.test.tsx" name="&lt;OverflowProvider&gt;&lt;DiffRenderer /&gt;&lt;/OverflowProvider&gt; &gt; should correctly render a new file with no file extension correctly" time="0.0386662">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolConfirmationMessage.test.tsx" timestamp="2025-08-05T15:31:47.397Z" hostname="LAPTOP-BGULKM6D" tests="2" failures="0" errors="0" skipped="0" time="0.0992037">
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should not display urls if prompt and url are the same" time="0.0707882">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolConfirmationMessage.test.tsx" name="ToolConfirmationMessage &gt; should display urls if prompt and url are different" time="0.0247348">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/messages/ToolMessage.test.tsx" timestamp="2025-08-05T15:31:47.397Z" hostname="LAPTOP-BGULKM6D" tests="11" failures="0" errors="0" skipped="0" time="0.3392041">
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders basic tool information" time="0.246992">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ✔ for Success status" time="0.0088976">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows o for Pending status" time="0.0083894">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows ? for Confirming status" time="0.0080319">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows - for Canceled status" time="0.0075189">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows x for Error status" time="0.0064756">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows paused spiner for Executing status when streamingState is Idle" time="0.0086148">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows paused spiner for Executing status when streamingState is WaitingForConfirmation" time="0.0065728">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; ToolStatusIndicator rendering &gt; shows MockRespondingSpinner for Executing status when streamingState is Responding" time="0.0073212">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders DiffRenderer for diff results" time="0.009274">
        </testcase>
        <testcase classname="src/ui/components/messages/ToolMessage.test.tsx" name="&lt;ToolMessage /&gt; &gt; renders emphasis correctly" time="0.0147203">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/MaxSizedBox.test.tsx" timestamp="2025-08-05T15:31:47.398Z" hostname="LAPTOP-BGULKM6D" tests="16" failures="0" errors="0" skipped="0" time="0.3530451">
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders children without truncation when they fit" time="0.1380327">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines when content exceeds maxHeight" time="0.0130095">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; hides lines at the end when content exceeds maxHeight and overflowDirection is bottom" time="0.0120864">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text that exceeds maxWidth" time="0.0080197">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles mixed wrapping and non-wrapping segments" time="0.0810997">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles words longer than maxWidth by splitting them" time="0.01747">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; does not truncate when maxHeight is undefined" time="0.0064601">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden" time="0.0050834">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; shows plural &quot;lines&quot; when more than one line is hidden and overflowDirection is bottom" time="0.0069739">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; renders an empty box for empty children" time="0.003234">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte unicode characters correctly" time="0.0052251">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; wraps text with multi-byte emoji characters correctly" time="0.0060544">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; accounts for additionalHiddenLinesCount" time="0.0052117">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; handles React.Fragment as a child" time="0.008184">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the top" time="0.0143296">
        </testcase>
        <testcase classname="src/ui/components/shared/MaxSizedBox.test.tsx" name="&lt;MaxSizedBox /&gt; &gt; clips a long single text child from the bottom" time="0.0156179">
        </testcase>
    </testsuite>
    <testsuite name="src/ui/components/shared/text-buffer.test.ts" timestamp="2025-08-05T15:31:47.400Z" hostname="LAPTOP-BGULKM6D" tests="60" failures="0" errors="0" skipped="0" time="0.8960018">
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with empty text and cursor at (0,0) by default" time="0.1077513">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with provided initialText" time="0.0132651">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with initialText and initialCursorOffset" time="0.0082048">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines" time="0.0099404">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines with multiple spaces" time="0.009355">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should wrap visual lines even without spaces" time="0.0122721">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Initialization &gt; should initialize with multi-byte unicode characters and correct cursor offset" time="0.0089236">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert a character and update cursor" time="0.0994161">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; insert: should insert text in the middle of a line" time="0.0114825">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; newline: should create a new line and move cursor" time="0.0098305">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; backspace: should delete char to the left or merge lines" time="0.0150365">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Basic Editing &gt; del: should delete char to the right or merge lines" time="0.0095242">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should prepend @ to a valid file path on insert" time="0.0072999">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to an invalid file path on insert" time="0.0069615">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should handle quoted paths" time="0.0063838">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Drag and Drop File Paths &gt; should not prepend @ to short text that is not a path" time="0.0056099">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: left/right should work within and across visual lines (due to wrapping)" time="0.0194524">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: up/down should preserve preferred visual column" time="0.0217733">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Cursor Movement &gt; move: home/end should go to visual line start/end" time="0.0146564">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should wrap long lines correctly into visualLines" time="0.0074181">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Visual Layout &amp; Viewport &gt; should update visualScrollRow when visualCursor moves out of viewport" time="0.0170026">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo an insert operation" time="0.0102478">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Undo/Redo &gt; should undo and redo a newline operation" time="0.0113586">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; insert: should correctly handle multi-byte unicode characters" time="0.0061954">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; backspace: should correctly delete multi-byte unicode characters" time="0.0092406">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Unicode Handling &gt; move: left/right should treat multi-byte chars as single units for visual cursor" time="0.0110812">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should insert printable characters" time="0.0109383">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Enter&quot; key as newline" time="0.0069267">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle &quot;Backspace&quot; key" time="0.0082559">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle multiple delete characters in one input" time="0.009856">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts that contain delete characters " time="0.0071226">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle inserts with a mix of regular and delete characters " time="0.0080512">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle arrow keys for movement" time="0.0088495">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should strip ANSI escape codes when pasting text" time="0.0070659">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should handle VSCode terminal Shift+Enter as newline" time="0.0053307">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; handleInput &gt; should correctly handle repeated pasting of long text" time="0.1519549">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a single-line range with single-line text" time="0.0106288">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should replace a multi-line range with single-line text" time="0.0077339">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should delete a range when replacing with an empty string" time="0.0068595">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the beginning of the text" time="0.0067334">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing at the end of the text" time="0.0068302">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle replacing the entire buffer content" time="0.0068435">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should correctly replace with unicode characters" time="0.006938">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; should handle invalid range by returning false and not changing text" time="0.0178394">
            <system-err>
Invalid range provided to replaceRange {
  startRow: [33m0[39m,
  startCol: [33m5[39m,
  endRow: [33m0[39m,
  endCol: [33m3[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}
Invalid range provided to replaceRange {
  startRow: [33m1[39m,
  startCol: [33m0[39m,
  endRow: [33m0[39m,
  endCol: [33m0[39m,
  linesLength: [33m1[39m,
  endRowLineLength: [33m4[39m
}

            </system-err>
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; replaceRange &gt; replaceRange: multiple lines with a single character" time="0.0510057">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip ANSI escape codes from input" time="0.0184022">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip control characters from input" time="0.0086803">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should strip mixed ANSI and control characters from input" time="0.0079155">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should not strip standard characters or newlines" time="0.0104487">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="useTextBuffer &gt; Input Sanitization &gt; should sanitize pasted text via handleInput" time="0.0079524">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should return [0,0] for offset 0" time="0.0010394">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle single line text" time="0.0014787">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-line text" time="0.0024098">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty lines" time="0.0016169">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text ending with a newline" time="0.0011659">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle text starting with a newline" time="0.0010769">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle empty string input" time="0.0009316">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle multi-byte unicode characters correctly" time="0.0022162">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset exactly at newline character" time="0.0009356">
        </testcase>
        <testcase classname="src/ui/components/shared/text-buffer.test.ts" name="offsetToLogicalPos &gt; should handle offset in the middle of a multi-byte character (should place at start of that char)" time="0.0010246">
        </testcase>
    </testsuite>
</testsuites>
