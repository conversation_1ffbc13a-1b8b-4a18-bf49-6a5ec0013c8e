{"version": 3, "file": "agentSelector.js", "sourceRoot": "", "sources": ["../../../src/config/agentSelector.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD;;GAEG;AACH,MAAM,OAAO,aAAa;IAIJ;IAHZ,YAAY,CAAe;IAC3B,WAAW,GAAiB,IAAI,CAAC;IAEzC,YAAoB,YAAoB;QAApB,iBAAY,GAAZ,YAAY,CAAQ;QACtC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvD,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;aAC5B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;aACnB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAsB;QACnC,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YACxB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACpE,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,SAAiB,EAAE,gBAAwB;QAClE,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QAE3C,0BAA0B;QAC1B,MAAM,YAAY,GAAG;YACnB,mFAAmF;YACnF,sDAAsD;SACvD,CAAC;QAEF,MAAM,cAAc,GAAa,EAAE,CAAC;QACpC,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACzC,IAAI,OAAO,EAAE,CAAC;gBACZ,cAAc,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,IAAI,QAA4B,CAAC;QACjC,IAAI,oDAAoD,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1E,QAAQ,GAAG,SAAS,CAAC;QACvB,CAAC;aAAM,IAAI,qDAAqD,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAClF,QAAQ,GAAG,eAAe,CAAC;QAC7B,CAAC;aAAM,IAAI,oDAAoD,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACjF,QAAQ,GAAG,aAAa,CAAC;QAC3B,CAAC;aAAM,IAAI,kCAAkC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/D,QAAQ,GAAG,WAAW,CAAC;QACzB,CAAC;aAAM,IAAI,wCAAwC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,QAAQ,GAAG,aAAa,CAAC;QAC3B,CAAC;QAED,OAAO;YACL,SAAS;YACT,gBAAgB;YAChB,cAAc,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;YACtE,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,KAAY,EAAE,OAAqB;QAC7D,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,6BAA6B;QAC7B,IAAI,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;YACnG,UAAU,IAAI,GAAG,CAAC;YAClB,OAAO,CAAC,IAAI,CAAC,qCAAqC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,yBAAyB;QACzB,IAAI,KAAK,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YACnD,MAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CACtE,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAC3C,CAAC;YAEF,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;gBAC1D,OAAO,CAAC,IAAI,CAAC,qBAAqB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC1D,MAAM,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACxD,KAAK,CAAC,QAAQ,CAAC,YAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;gBAC1C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;gBAC5D,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC,CAAC,CACH,CAAC;YAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;gBACxD,OAAO,CAAC,IAAI,CAAC,0BAA0B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YAC5B,MAAM,aAAa,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,sCAAsC;YAClG,UAAU,IAAI,aAAa,CAAC;YAC5B,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CAAC,wBAAwB,KAAK,CAAC,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAED,uCAAuC;QACvC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;QAElD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAqB;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAChC,SAAS;YACX,CAAC;YAED,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAEzE,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC,CAAC,oBAAoB;gBAC1C,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK;oBACL,UAAU;oBACV,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,qCAAqC;QACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;QAEpD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,SAAiB,EAAE,gBAAwB;QACvD,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC;QACnE,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAEjD,IAAI,YAAoC,CAAC;QACzC,IAAI,WAA+B,CAAC;QAEpC,iDAAiD;QACjD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;YACtD,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,WAAW,GAAG,2BAA2B,YAAY,CAAC,KAAK,CAAC,IAAI,eAAe,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACnH,CAAC;aAAM,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,WAAW,GAAG,SAAS,OAAO,CAAC,MAAM,mBAAmB,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC;QACxG,CAAC;aAAM,CAAC;YACN,WAAW,GAAG,wCAAwC,CAAC;QACzD,CAAC;QAED,OAAO;YACL,WAAW,EAAE,OAAO;YACpB,YAAY;YACZ,WAAW;SACZ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,SAAiB,EAAE,KAAa;QAC/C,MAAM,WAAW,GAAG,KAAK,IAAI,IAAI,CAAC,WAAW,CAAC;QAE9C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,uCAAuC;QACvC,MAAM,YAAY,GAAG;UACf,WAAW,CAAC,IAAI;QAClB,WAAW,CAAC,WAAW;aAClB,WAAW,CAAC,aAAa;iBACrB,WAAW,CAAC,YAAY;;;EAGvC,WAAW,CAAC,MAAM;;;;gBAIJ,SAAS,EAAE,CAAC;QAExB,OAAO,YAAY,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,cAAc;QAMZ,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAClD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;QAEpD,OAAO;YACL,WAAW;YACX,aAAa,EAAE,eAAe,CAAC,MAAM;YACrC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;SACrD,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAAY;QAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACrC,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC5C,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS;YACtC,KAAK,CAAC,EAAE,CAAC,WAAW,EAAE,KAAK,SAAS,CACrC,IAAI,IAAI,CAAC;IACZ,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;IACtE,CAAC;CACF"}