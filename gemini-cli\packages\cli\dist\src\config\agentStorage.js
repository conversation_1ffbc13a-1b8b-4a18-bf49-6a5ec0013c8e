/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import * as fs from 'fs';
import * as path from 'path';
import { homedir } from 'os';
import { getErrorMessage } from 'codecraft-cli-core';
import stripJsonComments from 'strip-json-comments';
import { AGENT_TEMPLATES } from './agents.js';
import { SETTINGS_DIRECTORY_NAME } from './settings.js';
export const AGENTS_DIRECTORY_NAME = 'agents';
export const USER_AGENTS_DIR = path.join(homedir(), SETTINGS_DIRECTORY_NAME, AGENTS_DIRECTORY_NAME);
export const AGENT_FILE_EXTENSION = '.json';
/**
 * Get the workspace agents directory for a given workspace
 */
export function getWorkspaceAgentsDir(workspaceDir) {
    return path.join(workspaceDir, SETTINGS_DIRECTORY_NAME, AGENTS_DIRECTORY_NAME);
}
/**
 * Get all possible agent directories (user and workspace)
 */
export function getAgentDirectories(workspaceDir) {
    return [
        USER_AGENTS_DIR,
        getWorkspaceAgentsDir(workspaceDir),
    ];
}
/**
 * Ensure agent directories exist
 */
export function ensureAgentDirectories(workspaceDir) {
    const directories = getAgentDirectories(workspaceDir);
    for (const dir of directories) {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
    }
}
/**
 * Generate a unique agent ID
 */
export function generateAgentId(name) {
    const sanitized = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const timestamp = Date.now().toString(36);
    return `${sanitized}-${timestamp}`;
}
/**
 * Get the file path for an agent
 */
export function getAgentFilePath(agentId, isUserAgent, workspaceDir) {
    const dir = isUserAgent ? USER_AGENTS_DIR : getWorkspaceAgentsDir(workspaceDir);
    return path.join(dir, `${agentId}${AGENT_FILE_EXTENSION}`);
}
/**
 * Validate agent configuration
 */
export function validateAgent(agent) {
    const errors = [];
    if (!agent.id || typeof agent.id !== 'string' || agent.id.trim() === '') {
        errors.push('Agent ID is required and must be a non-empty string');
    }
    if (!agent.name || typeof agent.name !== 'string' || agent.name.trim() === '') {
        errors.push('Agent name is required and must be a non-empty string');
    }
    if (!agent.description || typeof agent.description !== 'string') {
        errors.push('Agent description is required and must be a string');
    }
    if (!agent.expertiseArea || typeof agent.expertiseArea !== 'string') {
        errors.push('Agent expertise area is required and must be a string');
    }
    if (!agent.prompt || typeof agent.prompt !== 'string' || agent.prompt.trim() === '') {
        errors.push('Agent prompt is required and must be a non-empty string');
    }
    if (!agent.workingStyle || typeof agent.workingStyle !== 'string') {
        errors.push('Agent working style is required and must be a string');
    }
    return errors;
}
/**
 * Create a new agent from template
 */
export function createAgentFromTemplate(templateKey, customName, customId) {
    const template = AGENT_TEMPLATES[templateKey];
    const now = new Date().toISOString();
    const agentName = customName || template.name;
    const agentId = customId || generateAgentId(agentName);
    return {
        id: agentId,
        name: agentName,
        description: template.description,
        expertiseArea: template.expertiseArea,
        prompt: template.prompt,
        workingStyle: template.workingStyle,
        settings: {
            ...template.settings,
            autoSuggest: template.settings.autoSuggest ?? true,
            priority: template.settings.priority ?? 5,
            allowCombination: template.settings.allowCombination ?? true,
        },
        metadata: {
            createdAt: now,
            updatedAt: now,
            version: '1.0.0',
            createdBy: 'template',
            tags: [template.expertiseArea],
        },
        enabled: true,
    };
}
/**
 * Save an agent to file
 */
export async function saveAgent(agent, isUserAgent, workspaceDir) {
    const errors = validateAgent(agent);
    if (errors.length > 0) {
        throw new Error(`Invalid agent configuration: ${errors.join(', ')}`);
    }
    const filePath = getAgentFilePath(agent.id, isUserAgent, workspaceDir);
    // Ensure directory exists
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
    // Update metadata
    const updatedAgent = {
        ...agent,
        metadata: {
            ...agent.metadata,
            updatedAt: new Date().toISOString(),
        },
    };
    try {
        fs.writeFileSync(filePath, JSON.stringify(updatedAgent, null, 2), 'utf-8');
    }
    catch (error) {
        throw new Error(`Failed to save agent ${agent.id}: ${getErrorMessage(error)}`);
    }
}
/**
 * Load a single agent from file
 */
export function loadAgentFromFile(filePath) {
    try {
        if (!fs.existsSync(filePath)) {
            return {
                message: 'Agent file does not exist',
                path: filePath,
            };
        }
        const content = fs.readFileSync(filePath, 'utf-8');
        const agentData = JSON.parse(stripJsonComments(content));
        const errors = validateAgent(agentData);
        if (errors.length > 0) {
            return {
                message: `Invalid agent configuration: ${errors.join(', ')}`,
                path: filePath,
                agentId: agentData.id,
            };
        }
        return {
            agent: agentData,
            path: filePath,
        };
    }
    catch (error) {
        return {
            message: `Failed to load agent: ${getErrorMessage(error)}`,
            path: filePath,
        };
    }
}
/**
 * Load all agents from directories
 */
export function loadAllAgents(workspaceDir) {
    const agents = [];
    const errors = [];
    const directories = getAgentDirectories(workspaceDir);
    for (const dir of directories) {
        if (!fs.existsSync(dir)) {
            continue;
        }
        try {
            const files = fs.readdirSync(dir);
            const agentFiles = files.filter(file => file.endsWith(AGENT_FILE_EXTENSION));
            for (const file of agentFiles) {
                const filePath = path.join(dir, file);
                const result = loadAgentFromFile(filePath);
                if ('agent' in result) {
                    agents.push(result);
                }
                else {
                    errors.push(result);
                }
            }
        }
        catch (error) {
            errors.push({
                message: `Failed to read agents directory: ${getErrorMessage(error)}`,
                path: dir,
            });
        }
    }
    return { agents, errors };
}
/**
 * Delete an agent file
 */
export async function deleteAgent(agentId, workspaceDir) {
    const directories = getAgentDirectories(workspaceDir);
    for (const dir of directories) {
        const filePath = path.join(dir, `${agentId}${AGENT_FILE_EXTENSION}`);
        if (fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
                return true;
            }
            catch (error) {
                throw new Error(`Failed to delete agent ${agentId}: ${getErrorMessage(error)}`);
            }
        }
    }
    return false; // Agent not found
}
/**
 * Check if an agent exists
 */
export function agentExists(agentId, workspaceDir) {
    const directories = getAgentDirectories(workspaceDir);
    for (const dir of directories) {
        const filePath = path.join(dir, `${agentId}${AGENT_FILE_EXTENSION}`);
        if (fs.existsSync(filePath)) {
            return true;
        }
    }
    return false;
}
/**
 * Find an agent by ID
 */
export function findAgent(agentId, workspaceDir) {
    const directories = getAgentDirectories(workspaceDir);
    for (const dir of directories) {
        const filePath = path.join(dir, `${agentId}${AGENT_FILE_EXTENSION}`);
        if (fs.existsSync(filePath)) {
            const result = loadAgentFromFile(filePath);
            if ('agent' in result) {
                return result;
            }
        }
    }
    return null;
}
//# sourceMappingURL=agentStorage.js.map