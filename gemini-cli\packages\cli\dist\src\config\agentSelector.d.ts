/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Agent, AgentContext, AgentMatch, AgentSuggestion } from './agents.js';
/**
 * Agent selection and application logic
 */
export declare class AgentSelector {
    private workspaceDir;
    private loadedAgents;
    private activeAgent;
    constructor(workspaceDir: string);
    /**
     * Refresh the loaded agents
     */
    refresh(): void;
    /**
     * Get all available agents
     */
    getAvailableAgents(): Agent[];
    /**
     * Get the currently active agent
     */
    getActiveAgent(): Agent | null;
    /**
     * Set the active agent
     */
    setActiveAgent(agentId: string | null): boolean;
    /**
     * Analyze user input to extract context for agent matching
     */
    private analyzeUserInput;
    /**
     * Calculate confidence score for an agent match
     */
    private calculateConfidence;
    /**
     * Find matching agents for the given context
     */
    findMatchingAgents(context: AgentContext): AgentMatch[];
    /**
     * Get agent suggestions for user input
     */
    suggestAgents(userInput: string, workingDirectory: string): AgentSuggestion;
    /**
     * Apply agent prompt to user input
     */
    applyAgentPrompt(userInput: string, agent?: Agent): string;
    /**
     * Get agent status information
     */
    getAgentStatus(): {
        totalAgents: number;
        enabledAgents: number;
        activeAgent: Agent | null;
        errors: string[];
    };
    /**
     * Find agent by name (case-insensitive)
     */
    findAgentByName(name: string): Agent | null;
    /**
     * Get loading errors
     */
    getErrors(): string[];
}
