/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { Agent, AgentFile, AgentError, LoadedAgents, AgentTemplateKey } from './agents.js';
export declare const AGENTS_DIRECTORY_NAME = "agents";
export declare const USER_AGENTS_DIR: string;
export declare const AGENT_FILE_EXTENSION = ".json";
/**
 * Get the workspace agents directory for a given workspace
 */
export declare function getWorkspaceAgentsDir(workspaceDir: string): string;
/**
 * Get all possible agent directories (user and workspace)
 */
export declare function getAgentDirectories(workspaceDir: string): string[];
/**
 * Ensure agent directories exist
 */
export declare function ensureAgentDirectories(workspaceDir: string): void;
/**
 * Generate a unique agent ID
 */
export declare function generateAgentId(name: string): string;
/**
 * Get the file path for an agent
 */
export declare function getAgentFilePath(agentId: string, isUserAgent: boolean, workspaceDir: string): string;
/**
 * Validate agent configuration
 */
export declare function validateAgent(agent: Partial<Agent>): string[];
/**
 * Create a new agent from template
 */
export declare function createAgentFromTemplate(templateKey: AgentTemplateKey, customName?: string, customId?: string): Agent;
/**
 * Save an agent to file
 */
export declare function saveAgent(agent: Agent, isUserAgent: boolean, workspaceDir: string): Promise<void>;
/**
 * Load a single agent from file
 */
export declare function loadAgentFromFile(filePath: string): AgentFile | AgentError;
/**
 * Load all agents from directories
 */
export declare function loadAllAgents(workspaceDir: string): LoadedAgents;
/**
 * Delete an agent file
 */
export declare function deleteAgent(agentId: string, workspaceDir: string): Promise<boolean>;
/**
 * Check if an agent exists
 */
export declare function agentExists(agentId: string, workspaceDir: string): boolean;
/**
 * Find an agent by ID
 */
export declare function findAgent(agentId: string, workspaceDir: string): AgentFile | null;
