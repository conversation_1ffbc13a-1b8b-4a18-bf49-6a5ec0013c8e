/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Agent } from '../../config/agents.js';
export interface AgentStatusProps {
    /** Currently active agent, if any */
    activeAgent: Agent | null;
    /** Whether to show the status indicator */
    show?: boolean;
    /** Compact mode for smaller display */
    compact?: boolean;
}
/**
 * Component to display the current agent status
 */
export declare const AgentStatus: React.FC<AgentStatusProps>;
/**
 * Component to display agent suggestions
 */
export interface AgentSuggestionsProps {
    /** List of suggested agents */
    suggestions: Array<{
        agent: Agent;
        confidence: number;
        reasons: string[];
    }>;
    /** Callback when an agent is selected */
    onSelectAgent?: (agentId: string) => void;
    /** Whether to show the suggestions */
    show?: boolean;
}
export declare const AgentSuggestions: React.FC<AgentSuggestionsProps>;
/**
 * Component to display a list of available agents
 */
export interface AgentListProps {
    /** List of available agents */
    agents: Agent[];
    /** Currently active agent */
    activeAgent: Agent | null;
    /** Whether to show the list */
    show?: boolean;
    /** Compact mode */
    compact?: boolean;
}
export declare const AgentList: React.FC<AgentListProps>;
export default AgentStatus;
