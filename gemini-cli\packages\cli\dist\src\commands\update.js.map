{"version": 3, "file": "update.js", "sourceRoot": "", "sources": ["../../../src/commands/update.tsx"], "names": [], "mappings": ";AAAA;;;;GAIG;AAEH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAChC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;AAClC,OAAO,EAAE,IAAI,EAAE,MAAM,MAAM,CAAC;AAM5B,MAAM,UAAU,aAAa,CAAC,EAAE,MAAM,EAAsB;IAC1D,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,CAA+D,UAAU,CAAC,CAAC;IACrH,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;IACxE,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAS,EAAE,CAAC,CAAC;IACvE,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAS,EAAE,CAAC,CAAC;IAErE,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,cAAc,EAAE,CAAC;IACnB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,iBAAiB,GAAG,GAAW,EAAE;QACrC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,EAAE,uBAAuB,CAAC,CAAC;YAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC;YAClE,OAAO,WAAW,CAAC,OAAO,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,KAAK,IAAqB,EAAE;QACnD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,QAAQ,CAAC,qCAAqC,EAAE;gBAC7D,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YACH,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,KAAK,IAAmB,EAAE;QAC1C,IAAI,CAAC;YACH,SAAS,CAAC,UAAU,CAAC,CAAC;YACtB,UAAU,CAAC,2BAA2B,CAAC,CAAC;YAExC,QAAQ,CAAC,0CAA0C,EAAE;gBACnD,KAAK,EAAE,SAAS;gBAChB,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,SAAS,CAAC,SAAS,CAAC,CAAC;YACrB,UAAU,CAAC,gCAAgC,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,OAAO,CAAC,CAAC;YACnB,UAAU,CAAC,kBAAkB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC3F,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;QAChC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;YACpC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAE3B,UAAU,CAAC,gCAAgC,CAAC,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,gBAAgB,EAAE,CAAC;YACxC,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzB,IAAI,OAAO,KAAK,MAAM,EAAE,CAAC;gBACvB,SAAS,CAAC,YAAY,CAAC,CAAC;gBACxB,UAAU,CAAC,sCAAsC,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACN,MAAM,SAAS,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,SAAS,CAAC,OAAO,CAAC,CAAC;YACnB,UAAU,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACzG,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,GAAG,EAAE;QAC1B,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,SAAS,CAAC,CAAC,OAAO,OAAO,CAAC;YAC/B,KAAK,OAAO,CAAC,CAAC,OAAO,KAAK,CAAC;YAC3B,KAAK,YAAY,CAAC,CAAC,OAAO,MAAM,CAAC;YACjC,OAAO,CAAC,CAAC,OAAO,QAAQ,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,UAAU,CAAC,CAAC,OAAO,IAAI,CAAC;YAC7B,KAAK,UAAU,CAAC,CAAC,OAAO,IAAI,CAAC;YAC7B,KAAK,SAAS,CAAC,CAAC,OAAO,GAAG,CAAC;YAC3B,KAAK,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;YACzB,KAAK,YAAY,CAAC,CAAC,OAAO,GAAG,CAAC;YAC9B,OAAO,CAAC,CAAC,OAAO,GAAG,CAAC;QACtB,CAAC;IACH,CAAC,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE;QACnB,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;YAC1E,MAAM,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC5B,MAAM,EAAE,CAAC;YACX,CAAC,EAAE,IAAI,CAAC,CAAC;YACT,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAErB,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,OAAO,EAAE,CAAC,aACpC,KAAC,GAAG,IAAC,YAAY,EAAE,CAAC,YAClB,KAAC,IAAI,IAAC,IAAI,QAAC,KAAK,EAAC,MAAM,qCAA4B,GAC/C,EAEN,MAAC,GAAG,IAAC,YAAY,EAAE,CAAC,aAClB,MAAC,IAAI,eAAE,aAAa,EAAE,SAAS,EAC/B,KAAC,IAAI,IAAC,KAAK,EAAE,cAAc,EAAE,YAAG,OAAO,GAAQ,IAC3C,EAEL,cAAc,IAAI,CACjB,MAAC,GAAG,IAAC,YAAY,EAAE,CAAC,aAClB,KAAC,IAAI,oCAAyB,EAC9B,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,YAAE,cAAc,GAAQ,IACxC,CACP,EAEA,aAAa,IAAI,CAChB,MAAC,GAAG,IAAC,YAAY,EAAE,CAAC,aAClB,KAAC,IAAI,mCAAwB,EAC7B,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,YAAE,aAAa,GAAQ,IACtC,CACP,EAEA,MAAM,KAAK,UAAU,IAAI,CACxB,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,+CAAsC,GACpD,CACP,EAEA,CAAC,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,YAAY,CAAC,IAAI,CAC1E,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,YACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,+DAAsD,GACpE,CACP,IACG,CACP,CAAC;AACJ,CAAC"}