import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import React from 'react';
import { Text, Box } from 'ink';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { join } from 'path';
export function UpdateCommand({ onExit }) {
    const [status, setStatus] = React.useState('checking');
    const [message, setMessage] = React.useState('Checking for updates...');
    const [currentVersion, setCurrentVersion] = React.useState('');
    const [latestVersion, setLatestVersion] = React.useState('');
    React.useEffect(() => {
        checkAndUpdate();
    }, []);
    const getCurrentVersion = () => {
        try {
            const packagePath = join(__dirname, '../../../package.json');
            const packageJson = JSON.parse(readFileSync(packagePath, 'utf8'));
            return packageJson.version;
        }
        catch (error) {
            return 'unknown';
        }
    };
    const getLatestVersion = async () => {
        try {
            const result = execSync('npm view @google/gemini-cli version', {
                encoding: 'utf8',
                timeout: 10000
            });
            return result.trim();
        }
        catch (error) {
            throw new Error('Failed to check latest version');
        }
    };
    const updateCLI = async () => {
        try {
            setStatus('updating');
            setMessage('Updating CodeCraft CLI...');
            execSync('npm install -g @google/gemini-cli@latest', {
                stdio: 'inherit',
                timeout: 60000
            });
            setStatus('success');
            setMessage('Update completed successfully!');
        }
        catch (error) {
            setStatus('error');
            setMessage(`Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };
    const checkAndUpdate = async () => {
        try {
            const current = getCurrentVersion();
            setCurrentVersion(current);
            setMessage('Checking for latest version...');
            const latest = await getLatestVersion();
            setLatestVersion(latest);
            if (current === latest) {
                setStatus('up-to-date');
                setMessage('CodeCraft CLI is already up to date!');
            }
            else {
                await updateCLI();
            }
        }
        catch (error) {
            setStatus('error');
            setMessage(`Failed to check for updates: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    };
    const getStatusColor = () => {
        switch (status) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'up-to-date': return 'blue';
            default: return 'yellow';
        }
    };
    const getStatusIcon = () => {
        switch (status) {
            case 'checking': return '🔍';
            case 'updating': return '⬇️';
            case 'success': return '✅';
            case 'error': return '❌';
            case 'up-to-date': return '✨';
            default: return '⏳';
        }
    };
    React.useEffect(() => {
        if (status === 'success' || status === 'error' || status === 'up-to-date') {
            const timer = setTimeout(() => {
                onExit();
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [status, onExit]);
    return (_jsxs(Box, { flexDirection: "column", padding: 1, children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { bold: true, color: "cyan", children: "CodeCraft CLI Update" }) }), _jsxs(Box, { marginBottom: 1, children: [_jsxs(Text, { children: [getStatusIcon(), " "] }), _jsx(Text, { color: getStatusColor(), children: message })] }), currentVersion && (_jsxs(Box, { marginBottom: 1, children: [_jsx(Text, { children: "Current version: " }), _jsx(Text, { color: "yellow", children: currentVersion })] })), latestVersion && (_jsxs(Box, { marginBottom: 1, children: [_jsx(Text, { children: "Latest version: " }), _jsx(Text, { color: "green", children: latestVersion })] })), status === 'updating' && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: "This may take a few moments..." }) })), (status === 'success' || status === 'error' || status === 'up-to-date') && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: "Press any key to continue or wait 3 seconds..." }) }))] }));
}
//# sourceMappingURL=update.js.map