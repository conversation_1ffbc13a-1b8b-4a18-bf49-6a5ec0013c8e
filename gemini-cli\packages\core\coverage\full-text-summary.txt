------------------------------|---------|----------|---------|---------|-------------------
File                          | % Stmts | % Branch | % Funcs | % Lines | Uncovered Line #s 
------------------------------|---------|----------|---------|---------|-------------------
All files                     |       0 |        0 |       0 |       0 |                   
 src                          |       0 |        0 |       0 |       0 |                   
  index.ts                    |       0 |        0 |       0 |       0 | 1-59              
 src/__mocks__/fs             |       0 |        0 |       0 |       0 |                   
  promises.ts                 |       0 |        0 |       0 |       0 | 1-48              
 src/analytics                |       0 |        0 |       0 |       0 |                   
  analytics-service.ts        |       0 |        0 |       0 |       0 | 1-261             
  index.ts                    |       0 |        0 |       0 |       0 | 1-9               
  supabase-client.ts          |       0 |        0 |       0 |       0 | 1-129             
  user-info.ts                |       0 |        0 |       0 |       0 | 1-89              
 src/code_assist              |       0 |        0 |       0 |       0 |                   
  codeAssist.ts               |       0 |        0 |       0 |       0 | 1-23              
  converter.ts                |       0 |        0 |       0 |       0 | 1-234             
  oauth2.ts                   |       0 |        0 |       0 |       0 | 1-202             
  server.ts                   |       0 |        0 |       0 |       0 | 1-172             
  setup.ts                    |       0 |        0 |       0 |       0 | 1-57              
  types.ts                    |       0 |        0 |       0 |       0 | 1-175             
 src/config                   |       0 |        0 |       0 |       0 |                   
  config.ts                   |       0 |        0 |       0 |       0 | 1-502             
  models.ts                   |       0 |        0 |       0 |       0 | 1-9               
 src/core                     |       0 |        0 |       0 |       0 |                   
  client.ts                   |       0 |        0 |       0 |       0 | 1-536             
  contentGenerator.ts         |       0 |        0 |       0 |       0 | 1-132             
  coreToolScheduler.ts        |       0 |        0 |       0 |       0 | 1-671             
  geminiChat.ts               |       0 |        0 |       0 |       0 | 1-622             
  geminiRequest.ts            |       0 |        0 |       0 |       0 | 1-71              
  logger.ts                   |       0 |        0 |       0 |       0 | 1-301             
  modelCheck.ts               |       0 |        0 |       0 |       0 | 1-68              
  ...teractiveToolExecutor.ts |       0 |        0 |       0 |       0 | 1-119             
  prompts.ts                  |       0 |        0 |       0 |       0 | 1-273             
  tokenLimits.ts              |       0 |        0 |       0 |       0 | 1-31              
  turn.ts                     |       0 |        0 |       0 |       0 | 1-290             
 src/services                 |       0 |        0 |       0 |       0 |                   
  fileDiscoveryService.ts     |       0 |        0 |       0 |       0 | 1-93              
  gitService.ts               |       0 |        0 |       0 |       0 | 1-122             
 src/telemetry                |       0 |        0 |       0 |       0 |                   
  constants.ts                |       0 |        0 |       0 |       0 | 1-22              
  index.ts                    |       0 |        0 |       0 |       0 | 1-40              
  loggers.ts                  |       0 |        0 |       0 |       0 | 1-304             
  metrics.ts                  |       0 |        0 |       0 |       0 | 1-202             
  sdk.ts                      |       0 |        0 |       0 |       0 | 1-137             
  types.ts                    |       0 |        0 |       0 |       0 | 1-217             
 ...telemetry/clearcut-logger |       0 |        0 |       0 |       0 |                   
  clearcut-logger.ts          |       0 |        0 |       0 |       0 | 1-397             
  event-metadata-key.ts       |       0 |        0 |       0 |       0 | 1-153             
 src/tools                    |       0 |        0 |       0 |       0 |                   
  diffOptions.ts              |       0 |        0 |       0 |       0 | 1-12              
  edit.ts                     |       0 |        0 |       0 |       0 | 1-480             
  glob.ts                     |       0 |        0 |       0 |       0 | 1-307             
  grep.ts                     |       0 |        0 |       0 |       0 | 1-544             
  ls.ts                       |       0 |        0 |       0 |       0 | 1-313             
  mcp-client.ts               |       0 |        0 |       0 |       0 | 1-384             
  mcp-tool.ts                 |       0 |        0 |       0 |       0 | 1-148             
  memoryTool.ts               |       0 |        0 |       0 |       0 | 1-222             
  modifiable-tool.ts          |       0 |        0 |       0 |       0 | 1-165             
  read-file.ts                |       0 |        0 |       0 |       0 | 1-171             
  read-many-files.ts          |       0 |        0 |       0 |       0 | 1-494             
  shell.ts                    |       0 |        0 |       0 |       0 | 1-365             
  tool-registry.ts            |       0 |        0 |       0 |       0 | 1-234             
  tools.ts                    |       0 |        0 |       0 |       0 | 1-246             
  web-fetch.ts                |       0 |        0 |       0 |       0 | 1-360             
  web-search.ts               |       0 |        0 |       0 |       0 | 1-195             
  write-file.ts               |       0 |        0 |       0 |       0 | 1-401             
 src/utils                    |       0 |        0 |       0 |       0 |                   
  LruCache.ts                 |       0 |        0 |       0 |       0 | 1-41              
  bfsFileSearch.ts            |       0 |        0 |       0 |       0 | 1-87              
  editCorrector.ts            |       0 |        0 |       0 |       0 | 1-627             
  editor.ts                   |       0 |        0 |       0 |       0 | 1-183             
  errorReporting.ts           |       0 |        0 |       0 |       0 | 1-117             
  errors.ts                   |       0 |        0 |       0 |       0 | 1-62              
  fetch.ts                    |       0 |        0 |       0 |       0 | 1-57              
  fileUtils.ts                |       0 |        0 |       0 |       0 | 1-290             
  ...tentResponseUtilities.ts |       0 |        0 |       0 |       0 | 1-119             
  getFolderStructure.ts       |       0 |        0 |       0 |       0 | 1-347             
  gitIgnoreParser.ts          |       0 |        0 |       0 |       0 | 1-79              
  gitUtils.ts                 |       0 |        0 |       0 |       0 | 1-73              
  memoryDiscovery.ts          |       0 |        0 |       0 |       0 | 1-310             
  messageInspectors.ts        |       0 |        0 |       0 |       0 | 1-15              
  nextSpeakerChecker.ts       |       0 |        0 |       0 |       0 | 1-151             
  paths.ts                    |       0 |        0 |       0 |       0 | 1-159             
  retry.ts                    |       0 |        0 |       0 |       0 | 1-272             
  schemaValidator.ts          |       0 |        0 |       0 |       0 | 1-58              
  session.ts                  |       0 |        0 |       0 |       0 | 1-9               
  testUtils.ts                |       0 |        0 |       0 |       0 | 1-87              
  user_id.ts                  |       0 |        0 |       0 |       0 | 1-58              
------------------------------|---------|----------|---------|---------|-------------------
