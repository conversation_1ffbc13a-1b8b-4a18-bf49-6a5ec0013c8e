{"version": 3, "file": "updateCheck.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/updateCheck.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,cAAc,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AAExD,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,cAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC9D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,MAAM,QAAQ,GAAG,cAAc,CAAC;YAC9B,GAAG,EAAE;gBACH,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B;YACD,mBAAmB;YACnB,mBAAmB,EAAE,CAAC;YACtB,mCAAmC;YACnC,uBAAuB,EAAE,IAAI;SAC9B,CAAC,CAAC;QAEH,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO,gCAAgC,QAAQ,CAAC,MAAM,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,CAAC,MAAM,wBAAwB,WAAW,CAAC,IAAI,YAAY,CAAC;QACjJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,CAAC,IAAI,CAAC,+BAA+B,GAAG,CAAC,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC"}