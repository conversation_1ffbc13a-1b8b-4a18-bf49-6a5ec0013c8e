/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * Agent configuration types and interfaces for the CodeCraft CLI agent system.
 * Agents are specialized AI assistants with custom prompts and behaviors.
 */
export interface AgentMetadata {
    /** When the agent was created */
    createdAt: string;
    /** When the agent was last modified */
    updatedAt: string;
    /** Version of the agent configuration format */
    version: string;
    /** Who created the agent (user, system, etc.) */
    createdBy?: string;
    /** Tags for categorizing agents */
    tags?: string[];
}
export interface AgentSettings {
    /** Whether this agent should be automatically suggested for relevant tasks */
    autoSuggest?: boolean;
    /** Priority level when multiple agents match (higher = more priority) */
    priority?: number;
    /** Keywords that trigger this agent's suggestion */
    triggerKeywords?: string[];
    /** File patterns this agent specializes in */
    filePatterns?: string[];
    /** Whether this agent can be used with other agents simultaneously */
    allowCombination?: boolean;
    /** Maximum context length for this agent */
    maxContextLength?: number;
}
export interface Agent {
    /** Unique identifier for the agent */
    id: string;
    /** Human-readable name */
    name: string;
    /** Brief description of what the agent does */
    description: string;
    /** Domain of expertise (e.g., "documentation", "testing", "code review") */
    expertiseArea: string;
    /** Core instruction set that defines the agent's behavior */
    prompt: string;
    /** Description of how the agent approaches tasks */
    workingStyle: string;
    /** Agent-specific configuration options */
    settings: AgentSettings;
    /** Metadata about the agent */
    metadata: AgentMetadata;
    /** Whether the agent is currently enabled */
    enabled: boolean;
}
export interface AgentFile {
    /** The agent configuration */
    agent: Agent;
    /** File path where the agent is stored */
    path: string;
}
export interface AgentError {
    /** Error message */
    message: string;
    /** Path to the agent file that caused the error */
    path: string;
    /** Agent ID if available */
    agentId?: string;
}
export interface LoadedAgents {
    /** Successfully loaded agents */
    agents: AgentFile[];
    /** Errors encountered while loading agents */
    errors: AgentError[];
    /** Currently active agent, if any */
    activeAgent?: Agent;
}
export interface AgentContext {
    /** The current user input/request */
    userInput: string;
    /** Current working directory */
    workingDirectory: string;
    /** Files mentioned in the request */
    mentionedFiles?: string[];
    /** Detected intent or task type */
    taskType?: string;
    /** Previous conversation context */
    conversationHistory?: string[];
}
export interface AgentMatch {
    /** The matching agent */
    agent: Agent;
    /** Confidence score (0-1) for how well this agent matches */
    confidence: number;
    /** Reasons why this agent was matched */
    reasons: string[];
}
export interface AgentSuggestion {
    /** Suggested agents ranked by relevance */
    suggestions: AgentMatch[];
    /** Whether an agent was automatically selected */
    autoSelected?: AgentMatch;
    /** Explanation of the selection process */
    explanation?: string;
}
/**
 * Built-in agent templates that users can create from
 */
export declare const AGENT_TEMPLATES: {
    readonly DOCUMENTATION: {
        readonly name: "Documentation AI";
        readonly description: "Specializes in creating and maintaining documentation";
        readonly expertiseArea: "documentation";
        readonly prompt: "You are a documentation specialist AI. Your role is to:\n- Create clear, comprehensive documentation\n- Maintain consistency in documentation style\n- Focus on user-friendly explanations\n- Include practical examples and use cases\n- Ensure documentation is up-to-date with code changes\n- Follow documentation best practices and standards";
        readonly workingStyle: "Methodical and thorough, with emphasis on clarity and completeness";
        readonly settings: {
            readonly autoSuggest: true;
            readonly priority: 8;
            readonly triggerKeywords: string[];
            readonly filePatterns: string[];
            readonly allowCombination: true;
        };
    };
    readonly CODE_REVIEWER: {
        readonly name: "Code Reviewer";
        readonly description: "Focuses on code quality, best practices, and security";
        readonly expertiseArea: "code review";
        readonly prompt: "You are a code review specialist AI. Your role is to:\n- Analyze code for bugs, security issues, and performance problems\n- Suggest improvements following best practices\n- Check for code consistency and maintainability\n- Identify potential edge cases and error conditions\n- Recommend refactoring opportunities\n- Ensure code follows established patterns and conventions";
        readonly workingStyle: "Analytical and detail-oriented, with focus on quality and security";
        readonly settings: {
            readonly autoSuggest: true;
            readonly priority: 9;
            readonly triggerKeywords: string[];
            readonly filePatterns: string[];
            readonly allowCombination: true;
        };
    };
    readonly TESTING_SPECIALIST: {
        readonly name: "Testing Specialist";
        readonly description: "Expert in writing and maintaining tests";
        readonly expertiseArea: "testing";
        readonly prompt: "You are a testing specialist AI. Your role is to:\n- Write comprehensive unit, integration, and end-to-end tests\n- Identify edge cases and test scenarios\n- Suggest testing strategies and frameworks\n- Help with test debugging and optimization\n- Ensure good test coverage and quality\n- Follow testing best practices and patterns";
        readonly workingStyle: "Systematic and thorough, with focus on comprehensive coverage";
        readonly settings: {
            readonly autoSuggest: true;
            readonly priority: 7;
            readonly triggerKeywords: string[];
            readonly filePatterns: string[];
            readonly allowCombination: true;
        };
    };
};
export type AgentTemplateKey = keyof typeof AGENT_TEMPLATES;
