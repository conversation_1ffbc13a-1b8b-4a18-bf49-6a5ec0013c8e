import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { Box, Text } from 'ink';
import { Colors } from '../colors.js';
import { GIT_COMMIT_INFO } from '../../generated/git-commit.js';
export const AboutBox = ({ cliVersion, osVersion, sandboxEnv, modelVersion, }) => (_jsxs(Box, { borderStyle: "round", borderColor: Colors.Gray, flexDirection: "column", padding: 1, marginY: 1, width: "100%", children: [_jsx(Box, { marginBottom: 1, children: _jsx(Text, { bold: true, color: Colors.AccentPurple, children: "About CodeCraft CLI" }) }), _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: "35%", children: _jsx(Text, { bold: true, color: Colors.LightBlue, children: "CLI Version" }) }), _jsx(Box, { children: _jsx(Text, { children: cliVersion }) })] }), GIT_COMMIT_INFO && !['N/A'].includes(GIT_COMMIT_INFO) && (_jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: "35%", children: _jsx(Text, { bold: true, color: Colors.LightBlue, children: "Git Commit" }) }), _jsx(Box, { children: _jsx(Text, { children: GIT_COMMIT_INFO }) })] })), _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: "35%", children: _jsx(Text, { bold: true, color: Colors.LightBlue, children: "Model" }) }), _jsx(Box, { children: _jsx(Text, { children: modelVersion }) })] }), _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: "35%", children: _jsx(Text, { bold: true, color: Colors.LightBlue, children: "Sandbox" }) }), _jsx(Box, { children: _jsx(Text, { children: sandboxEnv }) })] }), _jsxs(Box, { flexDirection: "row", children: [_jsx(Box, { width: "35%", children: _jsx(Text, { bold: true, color: Colors.LightBlue, children: "OS" }) }), _jsx(Box, { children: _jsx(Text, { children: osVersion }) })] })] }));
//# sourceMappingURL=AboutBox.js.map