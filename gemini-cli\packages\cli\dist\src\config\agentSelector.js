/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { loadAllAgents } from './agentStorage.js';
/**
 * Agent selection and application logic
 */
export class AgentSelector {
    workspaceDir;
    loadedAgents;
    activeAgent = null;
    constructor(workspaceDir) {
        this.workspaceDir = workspaceDir;
        this.loadedAgents = loadAllAgents(workspaceDir);
    }
    /**
     * Refresh the loaded agents
     */
    refresh() {
        this.loadedAgents = loadAllAgents(this.workspaceDir);
    }
    /**
     * Get all available agents
     */
    getAvailableAgents() {
        return this.loadedAgents.agents
            .map(af => af.agent)
            .filter(agent => agent.enabled);
    }
    /**
     * Get the currently active agent
     */
    getActiveAgent() {
        return this.activeAgent;
    }
    /**
     * Set the active agent
     */
    setActiveAgent(agentId) {
        if (agentId === null) {
            this.activeAgent = null;
            return true;
        }
        const agent = this.getAvailableAgents().find(a => a.id === agentId);
        if (agent) {
            this.activeAgent = agent;
            return true;
        }
        return false;
    }
    /**
     * Analyze user input to extract context for agent matching
     */
    analyzeUserInput(userInput, workingDirectory) {
        const lowerInput = userInput.toLowerCase();
        // Extract mentioned files
        const filePatterns = [
            /\b[\w\-\.\/]+\.(js|ts|py|java|cpp|c|go|rs|md|txt|json|yaml|yml|html|css|scss)\b/gi,
            /\b(README|CHANGELOG|LICENSE|Makefile|Dockerfile)\b/gi,
        ];
        const mentionedFiles = [];
        for (const pattern of filePatterns) {
            const matches = userInput.match(pattern);
            if (matches) {
                mentionedFiles.push(...matches);
            }
        }
        // Detect task type based on keywords
        let taskType;
        if (/\b(test|testing|spec|unit test|integration|e2e)\b/i.test(lowerInput)) {
            taskType = 'testing';
        }
        else if (/\b(docs?|documentation|readme|guide|manual|help)\b/i.test(lowerInput)) {
            taskType = 'documentation';
        }
        else if (/\b(review|check|analyze|audit|security|quality)\b/i.test(lowerInput)) {
            taskType = 'code review';
        }
        else if (/\b(debug|fix|bug|error|issue)\b/i.test(lowerInput)) {
            taskType = 'debugging';
        }
        else if (/\b(refactor|optimize|improve|clean)\b/i.test(lowerInput)) {
            taskType = 'refactoring';
        }
        return {
            userInput,
            workingDirectory,
            mentionedFiles: mentionedFiles.length > 0 ? mentionedFiles : undefined,
            taskType,
        };
    }
    /**
     * Calculate confidence score for an agent match
     */
    calculateConfidence(agent, context) {
        let confidence = 0;
        const reasons = [];
        // Check expertise area match
        if (context.taskType && agent.expertiseArea.toLowerCase().includes(context.taskType.toLowerCase())) {
            confidence += 0.4;
            reasons.push(`Expertise area matches task type: ${context.taskType}`);
        }
        // Check trigger keywords
        if (agent.settings.triggerKeywords) {
            const lowerInput = context.userInput.toLowerCase();
            const matchedKeywords = agent.settings.triggerKeywords.filter(keyword => lowerInput.includes(keyword.toLowerCase()));
            if (matchedKeywords.length > 0) {
                confidence += Math.min(0.3, matchedKeywords.length * 0.1);
                reasons.push(`Matched keywords: ${matchedKeywords.join(', ')}`);
            }
        }
        // Check file patterns
        if (agent.settings.filePatterns && context.mentionedFiles) {
            const matchedFiles = context.mentionedFiles.filter(file => agent.settings.filePatterns.some(pattern => {
                const regex = new RegExp(pattern.replace(/\*/g, '.*'), 'i');
                return regex.test(file);
            }));
            if (matchedFiles.length > 0) {
                confidence += Math.min(0.2, matchedFiles.length * 0.05);
                reasons.push(`Matched file patterns: ${matchedFiles.join(', ')}`);
            }
        }
        // Apply priority boost
        if (agent.settings.priority) {
            const priorityBoost = (agent.settings.priority - 5) * 0.02; // -0.1 to +0.1 based on priority 0-10
            confidence += priorityBoost;
            if (priorityBoost > 0) {
                reasons.push(`High priority agent (${agent.settings.priority})`);
            }
        }
        // Ensure confidence is between 0 and 1
        confidence = Math.max(0, Math.min(1, confidence));
        return { confidence, reasons };
    }
    /**
     * Find matching agents for the given context
     */
    findMatchingAgents(context) {
        const availableAgents = this.getAvailableAgents();
        const matches = [];
        for (const agent of availableAgents) {
            if (!agent.settings.autoSuggest) {
                continue;
            }
            const { confidence, reasons } = this.calculateConfidence(agent, context);
            if (confidence > 0.1) { // Minimum threshold
                matches.push({
                    agent,
                    confidence,
                    reasons,
                });
            }
        }
        // Sort by confidence (highest first)
        matches.sort((a, b) => b.confidence - a.confidence);
        return matches;
    }
    /**
     * Get agent suggestions for user input
     */
    suggestAgents(userInput, workingDirectory) {
        const context = this.analyzeUserInput(userInput, workingDirectory);
        const matches = this.findMatchingAgents(context);
        let autoSelected;
        let explanation;
        // Auto-select if there's a high-confidence match
        if (matches.length > 0 && matches[0].confidence > 0.7) {
            autoSelected = matches[0];
            explanation = `Automatically selected "${autoSelected.agent.name}" based on: ${autoSelected.reasons.join(', ')}`;
        }
        else if (matches.length > 0) {
            explanation = `Found ${matches.length} potential agent${matches.length > 1 ? 's' : ''} for this task`;
        }
        else {
            explanation = 'No specific agents found for this task';
        }
        return {
            suggestions: matches,
            autoSelected,
            explanation,
        };
    }
    /**
     * Apply agent prompt to user input
     */
    applyAgentPrompt(userInput, agent) {
        const activeAgent = agent || this.activeAgent;
        if (!activeAgent) {
            return userInput;
        }
        // Combine agent prompt with user input
        const agentContext = `
[AGENT: ${activeAgent.name}]
Role: ${activeAgent.description}
Expertise: ${activeAgent.expertiseArea}
Working Style: ${activeAgent.workingStyle}

Instructions:
${activeAgent.prompt}

---

User Request: ${userInput}`;
        return agentContext;
    }
    /**
     * Get agent status information
     */
    getAgentStatus() {
        const availableAgents = this.getAvailableAgents();
        const totalAgents = this.loadedAgents.agents.length;
        return {
            totalAgents,
            enabledAgents: availableAgents.length,
            activeAgent: this.activeAgent,
            errors: this.loadedAgents.errors.map(e => e.message),
        };
    }
    /**
     * Find agent by name (case-insensitive)
     */
    findAgentByName(name) {
        const lowerName = name.toLowerCase();
        return this.getAvailableAgents().find(agent => agent.name.toLowerCase() === lowerName ||
            agent.id.toLowerCase() === lowerName) || null;
    }
    /**
     * Get loading errors
     */
    getErrors() {
        return this.loadedAgents.errors.map(e => `${e.path}: ${e.message}`);
    }
}
//# sourceMappingURL=agentSelector.js.map