{"version": 3, "file": "useGitBranchName.js", "sourceRoot": "", "sources": ["../../../../src/ui/hooks/useGitBranchName.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AACzD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,SAAS,CAAC;AACzB,OAAO,UAAU,MAAM,kBAAkB,CAAC;AAC1C,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,MAAM,UAAU,gBAAgB,CAAC,GAAW;IAC1C,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAqB,SAAS,CAAC,CAAC;IAE5E,MAAM,eAAe,GAAG,WAAW,CACjC,GAAG,EAAE,CACH,IAAI,CACF,iCAAiC,EACjC,EAAE,GAAG,EAAE,EACP,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;QACzB,IAAI,KAAK,EAAE,CAAC;YACV,aAAa,CAAC,SAAS,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;QACD,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACxC,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE,CAAC;YAChC,aAAa,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,IAAI,CACF,4BAA4B,EAC5B,EAAE,GAAG,EAAE,EACP,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;gBACzB,IAAI,KAAK,EAAE,CAAC;oBACV,aAAa,CAAC,SAAS,CAAC,CAAC;oBACzB,OAAO;gBACT,CAAC;gBACD,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC,CACF,CAAC;QACJ,CAAC;IACH,CAAC,CACF,EACH,CAAC,GAAG,EAAE,aAAa,CAAC,CACrB,CAAC;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,eAAe,EAAE,CAAC,CAAC,gBAAgB;QAEnC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;QAC/D,IAAI,OAAiC,CAAC;QAEtC,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC;gBACH,iFAAiF;gBACjF,MAAM,UAAU,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC5D,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC,SAAiB,EAAE,EAAE;oBACxD,uEAAuE;oBACvE,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE,CAAC;wBACrD,6BAA6B;wBAC7B,eAAe,EAAE,CAAC;oBACpB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,0EAA0E;gBAC1E,0CAA0C;gBAC1C,wDAAwD;YAC1D,CAAC;QACH,CAAC,CAAC;QAEF,YAAY,EAAE,CAAC;QAEf,OAAO,GAAG,EAAE;YACV,OAAO,EAAE,KAAK,EAAE,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;IAE3B,OAAO,UAAU,CAAC;AACpB,CAAC"}