/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import { saveAgent, agentExists } from './agentStorage.js';
/**
 * Initialize default agents if they don't already exist
 */
export async function initializeDefaultAgents(workspaceDir) {
    const defaultAgents = [
        {
            name: 'Documentation AI',
            description: 'Specializes in creating and maintaining documentation',
            expertiseArea: 'documentation',
            prompt: `You are a documentation specialist AI. Your role is to:
- Create clear, comprehensive documentation
- Maintain consistency in documentation style
- Focus on user-friendly explanations
- Include practical examples and use cases
- Ensure documentation is up-to-date with code changes
- Follow documentation best practices and standards

When writing documentation:
- Use clear, concise language
- Structure content logically with proper headings
- Include code examples where relevant
- Consider the target audience's technical level
- Provide troubleshooting information when applicable`,
            workingStyle: 'Methodical and thorough, with emphasis on clarity and completeness',
            settings: {
                autoSuggest: true,
                priority: 8,
                triggerKeywords: ['docs', 'documentation', 'readme', 'guide', 'manual', 'help'],
                filePatterns: ['*.md', '*.rst', '*.txt', 'README*', 'CHANGELOG*', 'docs/**/*'],
                allowCombination: true,
            },
            enabled: true,
        },
        {
            name: 'Code Reviewer',
            description: 'Focuses on code quality, best practices, and security',
            expertiseArea: 'code review',
            prompt: `You are a code review specialist AI. Your role is to:
- Analyze code for bugs, security issues, and performance problems
- Suggest improvements following best practices
- Check for code consistency and maintainability
- Identify potential edge cases and error conditions
- Recommend refactoring opportunities
- Ensure code follows established patterns and conventions

When reviewing code:
- Look for security vulnerabilities
- Check for proper error handling
- Verify code follows SOLID principles
- Suggest performance optimizations
- Ensure proper testing coverage
- Recommend documentation improvements`,
            workingStyle: 'Analytical and detail-oriented, with focus on quality and security',
            settings: {
                autoSuggest: true,
                priority: 9,
                triggerKeywords: ['review', 'check', 'analyze', 'audit', 'security', 'quality'],
                filePatterns: ['*.js', '*.ts', '*.py', '*.java', '*.cpp', '*.c', '*.go', '*.rs'],
                allowCombination: true,
            },
            enabled: true,
        },
        {
            name: 'Testing Specialist',
            description: 'Expert in writing and maintaining tests',
            expertiseArea: 'testing',
            prompt: `You are a testing specialist AI. Your role is to:
- Write comprehensive unit, integration, and end-to-end tests
- Identify edge cases and test scenarios
- Suggest testing strategies and frameworks
- Help with test debugging and optimization
- Ensure good test coverage and quality
- Follow testing best practices and patterns

When creating tests:
- Cover happy path, edge cases, and error conditions
- Use descriptive test names and clear assertions
- Follow the AAA pattern (Arrange, Act, Assert)
- Mock external dependencies appropriately
- Ensure tests are fast, reliable, and maintainable
- Consider test pyramid principles`,
            workingStyle: 'Systematic and thorough, with focus on comprehensive coverage',
            settings: {
                autoSuggest: true,
                priority: 7,
                triggerKeywords: ['test', 'testing', 'spec', 'unit test', 'integration', 'e2e'],
                filePatterns: ['*.test.*', '*.spec.*', 'test/**/*', 'tests/**/*', '__tests__/**/*'],
                allowCombination: true,
            },
            enabled: true,
        },
    ];
    for (const agentTemplate of defaultAgents) {
        const agentId = generateDefaultAgentId(agentTemplate.name);
        // Only create if it doesn't already exist
        if (!agentExists(agentId, workspaceDir)) {
            const agent = {
                ...agentTemplate,
                id: agentId,
                metadata: {
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    version: '1.0.0',
                    createdBy: 'system',
                    tags: [agentTemplate.expertiseArea],
                },
            };
            try {
                await saveAgent(agent, false, workspaceDir); // Save as workspace agent
            }
            catch (error) {
                console.warn(`Failed to create default agent "${agent.name}":`, error);
            }
        }
    }
}
/**
 * Generate a consistent ID for default agents
 */
function generateDefaultAgentId(name) {
    return name.toLowerCase().replace(/[^a-z0-9]/g, '-') + '-default';
}
/**
 * Check if default agents need to be initialized
 */
export function shouldInitializeDefaultAgents(workspaceDir) {
    const defaultAgentIds = [
        'documentation-ai-default',
        'code-reviewer-default',
        'testing-specialist-default',
    ];
    // If any default agent is missing, we should initialize
    return defaultAgentIds.some(id => !agentExists(id, workspaceDir));
}
//# sourceMappingURL=defaultAgents.js.map