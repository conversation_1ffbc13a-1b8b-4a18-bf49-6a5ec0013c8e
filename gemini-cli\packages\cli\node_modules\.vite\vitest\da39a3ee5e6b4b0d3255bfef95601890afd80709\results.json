{"version": "3.2.1", "results": [[":src/ui/components/shared/text-buffer.test.ts", {"duration": 896.0018000000018, "failed": false}], [":src/ui/hooks/slashCommandProcessor.test.ts", {"duration": 414.9615999999987, "failed": false}], [":src/ui/hooks/useGeminiStream.test.tsx", {"duration": 1171.6000000000022, "failed": false}], [":src/ui/hooks/useToolScheduler.test.ts", {"duration": 312.40090000000055, "failed": false}], [":src/ui/hooks/atCommandProcessor.test.ts", {"duration": 175.0589, "failed": true}], [":src/config/settings.test.ts", {"duration": 87.10469999999987, "failed": false}], [":src/ui/App.test.tsx", {"duration": 2774.0298000000003, "failed": false}], [":src/config/config.test.ts", {"duration": 1127.4386999999988, "failed": false}], [":src/ui/hooks/useCompletion.integration.test.ts", {"duration": 1649.5288999999975, "failed": true}], [":src/config/agentStorage.test.ts", {"duration": 155.87319999999818, "failed": true}], [":src/ui/components/messages/DiffRenderer.test.tsx", {"duration": 2022.2825999999986, "failed": false}], [":src/config/agentSelector.test.ts", {"duration": 107.00180000000182, "failed": true}], [":src/ui/hooks/useAutoAcceptIndicator.test.ts", {"duration": 186.75659999999698, "failed": false}], [":src/ui/components/shared/MaxSizedBox.test.tsx", {"duration": 353.045100000003, "failed": false}], [":src/ui/hooks/useEditorSettings.test.ts", {"duration": 183.35099999999875, "failed": false}], [":src/ui/contexts/SessionContext.test.tsx", {"duration": 959.7428999999993, "failed": false}], [":src/config/config.integration.test.ts", {"duration": 215.4938000000002, "failed": false}], [":src/ui/hooks/useInputHistory.test.ts", {"duration": 626.5064999999995, "failed": false}], [":src/ui/hooks/useGitBranchName.test.ts", {"duration": 378.8600999999999, "failed": false}], [":src/nonInteractiveCli.test.ts", {"duration": 44.628600000000006, "failed": false}], [":src/ui/hooks/useShellHistory.test.ts", {"duration": 765.4400999999998, "failed": false}], [":src/ui/components/LoadingIndicator.test.tsx", {"duration": 179.66860000000088, "failed": false}], [":src/ui/hooks/useHistoryManager.test.ts", {"duration": 295.39950000000135, "failed": false}], [":src/ui/components/messages/ToolMessage.test.tsx", {"duration": 339.204099999999, "failed": false}], [":src/ui/components/InputPrompt.test.tsx", {"duration": 1371.499899999999, "failed": false}], [":src/ui/hooks/useConsoleMessages.test.ts", {"duration": 265.6226999999999, "failed": false}], [":src/ui/hooks/usePhraseCycler.test.ts", {"duration": 133.02399999999943, "failed": false}], [":src/ui/hooks/shellCommandProcessor.test.ts", {"duration": 276.7973000000002, "failed": false}], [":src/ui/utils/errorParsing.test.ts", {"duration": 24.166300000000774, "failed": false}], [":src/ui/hooks/useLoadingIndicator.test.ts", {"duration": 159.51490000000013, "failed": false}], [":src/gemini.test.tsx", {"duration": 26.247299999999086, "failed": false}], [":src/ui/hooks/useTimer.test.ts", {"duration": 280.1998000000003, "failed": false}], [":src/config/extension.test.ts", {"duration": 59.51649999999972, "failed": false}], [":src/ui/components/AuthDialog.test.tsx", {"duration": 382.27899999999863, "failed": false}], [":src/ui/components/HistoryItemDisplay.test.tsx", {"duration": 711.075499999999, "failed": true}], [":src/ui/components/Stats.test.tsx", {"duration": 313.04979999999705, "failed": false}], [":src/utils/startupWarnings.test.ts", {"duration": 0, "failed": false}], [":src/ui/utils/markdownUtilities.test.ts", {"duration": 15.406399999999849, "failed": false}], [":src/ui/utils/formatters.test.ts", {"duration": 17.08009999999922, "failed": false}], [":src/ui/components/StatsDisplay.test.tsx", {"duration": 351.7416000000012, "failed": false}], [":src/ui/components/messages/ToolConfirmationMessage.test.tsx", {"duration": 99.20369999999821, "failed": false}], [":src/ui/components/SessionSummaryDisplay.test.tsx", {"duration": 204.21219999999994, "failed": false}], [":src/ui/utils/textUtils.test.ts", {"duration": 13.894599999999627, "failed": false}]]}